import { useState } from "react";
import mobileProfileRequest from "../api/mobile-profile.api";

export const useDeleteMobileProfile = () => {
  const [loading, setLoading] = useState(false);

  async function request(
    params: {
      id: string;
    },
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading(true);
    const response = await mobileProfileRequest.delete(params);
    if (response.state === "success") {
      onSuccess && onSuccess(response.data);
      setLoading(false);
    } else {
      onError && onError(response.message);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
