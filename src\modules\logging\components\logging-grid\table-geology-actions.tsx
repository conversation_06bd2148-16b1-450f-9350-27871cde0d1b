import { useCallback } from "react";
import {
  UseFormGetValues,
  UseFormSetError,
  UseFormClearErrors,
  UseFormReset,
  UseFieldArrayUpdate,
  UseFieldArrayPrepend,
  UseFieldArrayRemove,
} from "react-hook-form";
import { toast } from "react-toastify";
import { v4 as uuidv4 } from "uuid";

import { FieldType } from "@/modules/geology-suite-field/const/enum";
import dataEntryRequest from "../../api/data-entry.api";
import { validateDepthConstraints } from "../../utils/logging.utils";
import { RowStatus } from "../render-field-types";
import {
  ErrorDetail,
  LoggingFormData,
  LoggingRowData,
  DataEntryValueData,
} from "./table-geology-types";

/**
 * Parameters interface for table editable actions
 */
export interface TableEditableActionsParams {
  // React Hook Form methods
  getValues: UseFormGetValues<LoggingFormData>;
  setError: UseFormSetError<LoggingFormData>;
  clearErrors: UseFormClearErrors<LoggingFormData>;
  reset: UseFormReset<LoggingFormData>;
  update: UseFieldArrayUpdate<LoggingFormData, "rows">;
  append: UseFieldArrayPrepend<LoggingFormData, "rows">;
  remove: UseFieldArrayRemove;

  // State setters
  setLoading: (loading: boolean) => void;
  setAddRowLoading: (loading: boolean) => void;
  setDataToDelete: React.Dispatch<React.SetStateAction<any[]>>;

  // Data and configuration
  dataToDelete: any[];
  geologySuiteFields?: any[];
  geologySuiteId?: number | null;
  drillholeId?: number | null;
  hasUnsavedChanges: boolean;
  geologySuiteDetail?: any; // Add geologySuiteDetail to access isGeotech

  // Callbacks
  refetchDataEntry: (isSaveLoggingLine: boolean) => Promise<void>;
  setScrollToRow?: (rowIndex: number | undefined) => void; // Function to trigger prop-based scrolling
  setScrollToColumn?: (columnIndex: number | undefined) => void; // Function to trigger prop-based scrolling
  onAfterRowMutation?: () => void; // Callback after a row is added, removed, or updated
}

/**
 * Return type for table editable actions hook
 */
export interface TableEditableActionsReturn {
  handleSave: () => Promise<void>;
  addNewRowWithSave: () => Promise<void>;
  copyRow: (rowIndex: number) => void;
  deleteRow: (rowIndex: number) => void;
  handleFieldChange: (rowIndex: number, fieldPath: string, value: any) => void;
  scrollToRowProp: (rowIndex: number) => void;
}

/**
 * Custom hook that provides all table action functionality
 */
export const useTableGeologyActions = (
  params: TableEditableActionsParams
): TableEditableActionsReturn => {
  const {
    getValues,
    setError,
    clearErrors,
    reset,
    update,
    append,
    remove,
    setLoading,
    setAddRowLoading,
    setDataToDelete,
    dataToDelete,
    geologySuiteFields,
    geologySuiteId,
    drillholeId,
    hasUnsavedChanges,
    geologySuiteDetail,
    refetchDataEntry,
    setScrollToRow,
    setScrollToColumn,
    onAfterRowMutation,
  } = params;

  // Helper function to scroll to a specific row using MultiGrid props (official approach)
  const scrollToRowProp = useCallback(
    (rowIndex: number) => {
      if (setScrollToRow) {
        // MultiGrid uses props for scrolling, not method calls
        // The rowIndex needs to account for the fixed header row
        // Since fixedRowCount=1, the data rows start at index 1
        const multiGridRowIndex = rowIndex + 1; // +1 because header is at index 0

        console.log(
          `Scrolling to row ${rowIndex} (MultiGrid rowIndex: ${multiGridRowIndex}) using scrollToRow prop`
        );

        // Set the scrollToRow prop to trigger scrolling
        setScrollToRow(multiGridRowIndex);

        // Clear the scrollToRow prop after a short delay to allow for future scrolling
        setTimeout(() => {
          setScrollToRow(undefined);
        }, 100);
      } else {
        console.warn("setScrollToRow function not available");
      }
    },
    [setScrollToRow]
  );

  // Validation callback
  const validateDepthConstraintsCallback = useCallback(
    (rows: LoggingRowData[]) => {
      return validateDepthConstraints(rows, geologySuiteFields);
    },
    [geologySuiteFields]
  );

  // Custom onChange handler to update row status when fields change
  const handleFieldChange = useCallback(
    (rowIndex: number, fieldPath: string, value: any) => {
      const currentRows = getValues("rows");
      const targetRow = currentRows[rowIndex];

      if (!targetRow) return;

      // Only update row status if the row is currently SAVED
      // NEW rows should remain NEW, EDITED rows should remain EDITED
      if (targetRow.rowStatus === RowStatus.SAVED) {
        // Capture the currently focused element before the update
        const activeElement = document.activeElement as HTMLElement;
        const focusedElementId = activeElement?.id;
        const focusedElementSelector = activeElement
          ? `#${activeElement.id}` ||
            `[data-testid="${activeElement.getAttribute("data-testid")}"]` ||
            activeElement.tagName.toLowerCase()
          : null;

        const updatedRow = {
          ...targetRow,
          rowStatus: RowStatus.EDITED,
        };

        // Update the row
        update(rowIndex, updatedRow);

        // Restore focus after the re-render completes
        if (focusedElementId || focusedElementSelector) {
          // Use setTimeout to ensure the DOM has been updated
          setTimeout(() => {
            let elementToFocus: HTMLElement | null = null;

            // Try to find the element by ID first
            if (focusedElementId) {
              elementToFocus = document.getElementById(focusedElementId);
            }

            // Fallback to selector if ID didn't work
            if (!elementToFocus && focusedElementSelector) {
              elementToFocus = document.querySelector(
                focusedElementSelector
              ) as HTMLElement;
            }

            // Focus the element if found
            if (elementToFocus && typeof elementToFocus.focus === "function") {
              elementToFocus.focus();
            }
          }, 0);
        }
      }
    },
    [getValues, update]
  );

  // Save functionality
  const handleSave = useCallback(async () => {
    setLoading(true);

    try {
      const currentRows = getValues("rows");

      // Clear any existing field errors first
      clearErrors();

      // Validate all rows
      const validationResult = validateDepthConstraintsCallback(currentRows);
      if (validationResult.errors.length > 0) {
        // Set field-specific errors in React Hook Form
        Object.entries(validationResult.fieldErrors).forEach(
          ([fieldPath, errorMessage]) => {
            setError(fieldPath as any, {
              type: "validation",
              message: errorMessage,
            });
          }
        );

        toast.error("Please correct the errors in the table before saving");

        // Skip toast notifications for validation errors since inline validation is displayed
        setLoading(false);
        return;
      }

      // Delete items first
      for (const item of dataToDelete) {
        await dataEntryRequest.delete({ id: item.id });
      }
      setDataToDelete([]);

      const rowToSave = currentRows.filter(
        (row) => row.rowStatus === RowStatus.EDITED
      );
      if (rowToSave.length > 0) {
        const payload = {
          drillholeId: drillholeId,
          geologySuiteId: geologySuiteId,
          dataEntries: rowToSave.map((row) => ({
            id: row.id,
            depthFrom: row.depthFrom,
            depthTo: row.depthTo,
          })),
        };

        const response = await dataEntryRequest.updateDepthDataEntry(
          payload as any
        );
        if (response?.state !== "success") {
          toast.error(response?.message);
          setLoading(false);
          return;
        }
      }

      // Process each row
      // Reserve the currentRows to save from bottom to top
      // const reserveRows = currentRows.reverse();

      let hasUpdateError = false;
      for (const [index, row] of currentRows.entries()) {
        if (row.rowStatus === RowStatus.SAVED) continue;

        // Prepare data entry values
        const dataEntryValues = row.dataEntryValues.map(
          (dataEntryValue, index) => {
            const geoField = (geologySuiteFields ?? [])?.[index];
            const baseValue = {
              fieldType: geoField?.geologyField?.type,
              geologysuiteFieldId: dataEntryValue.geologysuiteFieldId,
              // Note: valueId is excluded from API payload but kept in local state for UI functionality
            };

            // Add field-specific values based on field type
            switch (geoField?.geologyField?.type) {
              case FieldType.NumberField:
                return {
                  ...baseValue,
                  numberValue: dataEntryValue.numberValue,
                };
              case FieldType.Description:
                return {
                  ...baseValue,
                  description: dataEntryValue.description,
                };
              case FieldType.DateField:
                return { ...baseValue, dateValue: dataEntryValue.dateValue };
              case FieldType.Colour:
                return { ...baseValue, colourId: dataEntryValue.colourId };
              case FieldType.PickList:
                return {
                  ...baseValue,
                  pickListItemId: dataEntryValue.pickListItemId,
                };
              case FieldType.RockType:
                return {
                  ...baseValue,
                  rockTypeId: dataEntryValue.rockTypeId,
                  numberValue: dataEntryValue.numberValue,
                };
              case FieldType.RockSelect:
                return {
                  ...baseValue,
                  rockTypeId: dataEntryValue.rockTypeId,
                  numberValue: dataEntryValue.numberValue,
                };
              case FieldType.RockGroup:
                return { ...baseValue, rockTypeId: dataEntryValue.rockTypeId };
              case FieldType.RockTree:
                return { ...baseValue, rockNodeId: dataEntryValue.rockNodeId };
              default:
                return { ...baseValue, value: dataEntryValue.value };
            }
          }
        );

        const requestData = {
          geologySuiteId: row.geologySuiteId,
          drillholeId: row.drillholeId,
          depthFrom: row.depthFrom,
          depthTo: row.depthTo,
          dataEntryValues,
        };

        // Update row status to loading
        update(index, { ...row, loading: true });

        try {
          let response: any;

          if (row.rowStatus === RowStatus.NEW) {
            response = await dataEntryRequest.create(requestData);
          } else if (row.rowStatus === RowStatus.EDITED) {
            response = await dataEntryRequest.update({
              ...requestData,
              id: Number(row.id),
            });
          }

          // Check for success in the API wrapper format
          if (response?.state === "success") {
            update(index, {
              ...row,
              id: response.data?.id || row.id,
              rowStatus: RowStatus.SAVED,
              loading: false,
              errorDetail: undefined,
            });
          } else {
            // Handle API wrapper error format
            let errorMessage = `Failed to save row ${index + 1}`;

            if (response?.message) {
              errorMessage = response.message;
            } else if (response?.state === "error") {
              errorMessage = `Failed to save row ${index + 1}: ${
                response.message || "Unknown error"
              }`;
            }

            update(index, {
              ...row,
              loading: false,
              errorDetail: ErrorDetail.INVALID,
            });

            toast.error(errorMessage);
            hasUpdateError = true;
          }
        } catch (error: any) {
          // Handle raw API response errors or network errors
          let errorMessage = `Failed to save row ${index + 1}`;

          // Check for the API response structure mentioned by the user
          if (error?.response?.data) {
            const apiResponse = error.response.data;

            // Handle the specific API error format: { success: false, error: { message: "..." } }
            if (apiResponse.success === false && apiResponse.error?.message) {
              errorMessage = apiResponse.error.message;
            } else if (apiResponse.error?.message) {
              errorMessage = apiResponse.error.message;
            } else if (apiResponse.message) {
              errorMessage = apiResponse.message;
            }
          } else if (error?.message) {
            errorMessage = `Failed to save row ${index + 1}: ${error.message}`;
          }

          update(index, {
            ...row,
            loading: false,
            errorDetail: ErrorDetail.INVALID,
          });

          toast.error(errorMessage);
          hasUpdateError = true;
        }
      }
      if (!hasUpdateError) {
        toast.success("Data saved successfully");
      }

      await refetchDataEntry(false);

      // Reset form dirty state after successful save
      // This will clear the isDirty flag from React Hook Form
      const currentFormData = getValues();
      reset(currentFormData, { keepValues: true });
    } catch (error) {
      toast.error("Failed to save data");
    } finally {
      setLoading(false);
    }
  }, [
    dataToDelete,
    validateDepthConstraintsCallback,
    update,
    refetchDataEntry,
    getValues,
    setError,
    clearErrors,
    reset,
    setLoading,
    setDataToDelete,
  ]);

  // Enhanced addNewRow function that saves existing changes first
  const addNewRowWithSave = useCallback(async () => {
    if (!geologySuiteId || !drillholeId) return;

    // Check if there are unsaved changes
    if (hasUnsavedChanges) {
      setAddRowLoading(true);

      try {
        // Save all existing changes first
        await handleSave();

        // After successful save, proceed to add new row
        const newKey = uuidv4();
        const currentRows = getValues("rows");
        const lastRow = currentRows[currentRows.length - 1];
        const newDepthFrom = lastRow ? lastRow.depthTo : 0;

        // Check if it's a geotech suite
        const isGeotech = geologySuiteDetail?.isGeotech;

        let finalDepthFrom = newDepthFrom || 0;
        let finalDepthTo: number | undefined = undefined;

        // If it's geotech, call getNextBlock API to get depth values
        if (isGeotech) {
          try {
            // Get the last row's depthTo as the starting point
            const startDepth = lastRow ? lastRow.depthTo || 0 : 0;

            // Call API to get next block information
            const response = await dataEntryRequest.getNextBlock({
              geologySuiteId: Number(geologySuiteId),
              drillHoleId: Number(drillholeId),
              depthFrom: startDepth,
            });

            if (response.state === "success" && response.data) {
              finalDepthFrom = (response.data.blockFrom ?? startDepth) || 0;
              finalDepthTo = response.data ?? undefined;
            }
          } catch (error) {
            console.error("Error getting next block:", error);
            // Fallback to original logic if API fails
            finalDepthFrom = newDepthFrom || 0;
            finalDepthTo = undefined;
          }
        }

        const newRow: LoggingRowData = {
          id: newKey,
          valueId: newKey,
          depthFrom: finalDepthFrom,
          depthTo: finalDepthTo,
          rowStatus: RowStatus.NEW,
          geologySuiteId,
          drillholeId,
          dataEntryValues:
            geologySuiteFields?.map((field: any) => ({
              fieldType: field?.geologyField?.type,
              geologysuiteFieldId: field?.id,
              isMandatory: field?.isMandatory,
              valueId: uuidv4(),
              value: undefined,
              numberId: undefined,
              numberValue: undefined,
              pickListItemId: undefined,
              rockTypeId: undefined,
              colourId: undefined,
              dateValue: undefined,
              description: undefined,
              number: { id: undefined },
              rockNodeId: undefined,
            })) || [],
        };

        append(newRow);

        // Immediately scroll to the new row using direct virtualized table scrolling
        setTimeout(() => {
          const currentRows = getValues("rows");
          const newRowIndex = currentRows.length - 1;

          // Scroll to the new row first
          scrollToRowProp(newRowIndex);
          // Then focus on the depth from field after a short delay to ensure scrolling is complete
          setTimeout(() => {
            if (finalDepthFrom && !finalDepthTo) {
              const depthToInput = document.getElementById(`${newKey}-depthTo`);
              if (depthToInput) {
                (depthToInput as HTMLInputElement).focus();
              }
            } else {
              const depthFromInput = document.getElementById(
                `${newKey}-depthFrom`
              );
              if (depthFromInput) {
                (depthFromInput as HTMLInputElement).focus();
              }
            }
          }, 40); // Additional delay for focus after scroll
        }, 20); // Minimal delay to ensure row is appended
      } catch (error) {
        // Save failed, don't add new row
        toast.error(
          "Failed to save existing changes. Please fix errors before adding a new row."
        );
      } finally {
        setAddRowLoading(false);
      }
    } else {
      // No unsaved changes, proceed directly to add new row
      const newKey = uuidv4();
      const currentRows = getValues("rows");
      const lastRow = currentRows[currentRows.length - 1];
      const newDepthFrom = lastRow ? lastRow.depthTo : 0;

      // Check if it's a geotech suite
      const isGeotech = geologySuiteDetail?.isGeotech;

      let finalDepthFrom = newDepthFrom || 0;
      let finalDepthTo: number | undefined = undefined;

      // If it's geotech, call getNextBlock API to get depth values
      if (isGeotech) {
        try {
          // Get the last row's depthTo as the starting point
          const startDepth = lastRow ? lastRow.depthTo || 0 : 0;

          // Call API to get next block information
          const response = await dataEntryRequest.getNextBlock({
            geologySuiteId: Number(geologySuiteId),
            drillHoleId: Number(drillholeId),
            depthFrom: startDepth,
          });

          if (response.state === "success" && response.data) {
            finalDepthFrom = (response.data.blockFrom ?? startDepth) || 0;
            finalDepthTo = response.data ?? undefined;
          }
        } catch (error) {
          console.error("Error getting next block:", error);
          // Fallback to original logic if API fails
          finalDepthFrom = newDepthFrom || 0;
          finalDepthTo = undefined;
        }
      }

      const newRow: LoggingRowData = {
        id: newKey,
        valueId: newKey,
        depthFrom: finalDepthFrom,
        depthTo: finalDepthTo,
        rowStatus: RowStatus.NEW,
        geologySuiteId,
        drillholeId,
        dataEntryValues:
          geologySuiteFields?.map((field: any) => ({
            fieldType: field?.geologyField?.type,
            geologysuiteFieldId: field?.id,
            isMandatory: field?.isMandatory,
            valueId: uuidv4(),
            value: undefined,
            numberId: undefined,
            numberValue: undefined,
            pickListItemId: undefined,
            rockTypeId: undefined,
            colourId: undefined,
            dateValue: undefined,
            description: undefined,
            number: { id: undefined },
            rockNodeId: undefined,
          })) || [],
      };

      append(newRow);

      if (onAfterRowMutation) {
        onAfterRowMutation();
      }

      // Immediately scroll to the new row using direct virtualized table scrolling
      setTimeout(() => {
        const currentRows = getValues("rows");
        const newRowIndex = currentRows.length - 1;

        // Scroll to the new row first
        scrollToRowProp(newRowIndex);
        // Then focus on the depth from field after a short delay to ensure scrolling is complete
        setTimeout(() => {
          if (finalDepthFrom && !finalDepthTo) {
            const depthToInput = document.getElementById(`${newKey}-depthTo`);
            if (depthToInput) {
              (depthToInput as HTMLInputElement).focus();
            }
          } else {
            const depthFromInput = document.getElementById(
              `${newKey}-depthFrom`
            );
            if (depthFromInput) {
              (depthFromInput as HTMLInputElement).focus();
            }
          }
        }, 40); // Additional delay for focus after scroll
      }, 20); // Minimal delay to ensure row is appended
    }
  }, [
    geologySuiteId,
    drillholeId,
    hasUnsavedChanges,
    geologySuiteDetail,
    handleSave,
    getValues,
    geologySuiteFields,
    append,
    setAddRowLoading,
    scrollToRowProp,
  ]);

  // Copy row functionality
  const copyRow = useCallback(
    (rowIndex: number) => {
      const currentRows = getValues("rows");
      const rowToCopy = currentRows[rowIndex];
      if (!rowToCopy) return;

      // Get the last row in the list to get its depthTo
      const lastRow = currentRows[currentRows.length - 1];
      const newDepthFrom = lastRow ? lastRow.depthTo : 0;

      const newKey = uuidv4();
      const copiedRow: LoggingRowData = {
        ...rowToCopy,
        id: newKey,
        valueId: newKey,
        depthFrom: newDepthFrom, // Set depth from = depth to of the last row in list
        depthTo: undefined, // Set depth to = empty
        rowStatus: RowStatus.NEW,
        loading: false,
        errorDetail: undefined,
        dataEntryValues: rowToCopy.dataEntryValues.map((value) => ({
          ...value,
          valueId: uuidv4(),
        })),
      };

      append(copiedRow);
      if (onAfterRowMutation) {
        onAfterRowMutation();
      }

      // Immediately scroll to the copied row using direct virtualized table scrolling
      setTimeout(() => {
        const currentRows = getValues("rows");
        const newRowIndex = currentRows.length - 1;

        // Scroll to the copied row first
        scrollToRowProp(newRowIndex);

        // Then focus on the depth to field after a short delay to ensure scrolling is complete
        setTimeout(() => {
          const depthToInput = document.getElementById(`${newKey}-depthTo`);
          if (depthToInput) {
            (depthToInput as HTMLInputElement).focus();
          }
        }, 100); // Additional delay for focus after scroll
      }, 50); // Minimal delay to ensure row is appended
    },
    [getValues, append, scrollToRowProp, onAfterRowMutation]
  );

  // Delete row functionality
  const deleteRow = useCallback(
    (rowIndex: number) => {
      const currentRows = getValues("rows");
      const rowToDelete = currentRows[rowIndex];
      if (!rowToDelete) return;

      // If it's a saved row, add to delete queue
      if (rowToDelete.rowStatus === RowStatus.SAVED && rowToDelete.id) {
        setDataToDelete((prev) => [...prev, { id: rowToDelete.id }]);
      }

      remove(rowIndex);
      if (onAfterRowMutation) {
        onAfterRowMutation();
      }
    },
    [getValues, remove, setDataToDelete, onAfterRowMutation]
  );

  return {
    handleSave,
    addNewRowWithSave,
    copyRow,
    deleteRow,
    handleFieldChange,
    scrollToRowProp,
  };
};
