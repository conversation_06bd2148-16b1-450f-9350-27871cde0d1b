import { ImportFileType } from "../api/importData.api";
import { SuiteType } from "../interface/importData.type";

export const getImportFileTypeLabel = (importFileType: ImportFileType): string => {
  switch (importFileType) {
    case ImportFileType.Geology:
      return "Geology";
    case ImportFileType.Geophysics:
      return "Geophysics";
    case ImportFileType.Assay:
      return "Assay";
    case ImportFileType.DownholeSurvey:
      return "Downhole Survey";
    case ImportFileType.DrillHole:
      return "Drill Hole";
    default:
      return "Unknown";
  }
};

/**
 * Converts suite type string to ImportFileType enum value
 * @param suiteType - The suite type string from data.type
 * @returns The corresponding ImportFileType enum value
 */
export const getImportFileTypeFromSuiteType = (suiteType: string): ImportFileType => {
  switch (suiteType) {
    case SuiteType.GeophysicsSuite:
      return ImportFileType.Geophysics;
    case SuiteType.AssaySuite:
      return ImportFileType.Assay;
    case SuiteType.DownholeSurvey:
      return ImportFileType.DownholeSurvey;
    case SuiteType.DrillHole:
    case SuiteType.Drillhole:
      return ImportFileType.DrillHole;
    case SuiteType.Geology:
    default:
      return ImportFileType.Geology;
  }
};
