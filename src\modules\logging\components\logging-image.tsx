// React and core dependencies
import { useSearchParams } from "next/navigation";
import { Fragment, useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";

// UI Components
import { Button, Select, Tag, TreeSelect } from "antd";

// Utilities
import { isEmpty } from "lodash";

// Redux and state management
import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";

// Module-specific hooks
import { useGetListAttributeBySuiteId } from "@/modules/downhole-point/hooks/useGetAttributeBySuiteId";
import { useGetDownholeByProject } from "@/modules/downhole-point/hooks/useGetDownholeByProject";
import { useGetGeologySuite } from "@/modules/geology-suite/hooks/useGetGeologySuite";
import { useQueryGeologySuite } from "@/modules/geology-suite/hooks/useQueryGeologySuite";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import { useGetListDataEntry } from "../hooks/useGetListDataEntry";

// Redux actions and thunks
import { getDetailDrillhole } from "@/modules/drillhole/redux/drillholeSlice/thunks";
import { getGeologySuite } from "@/modules/geology-suite/redux/thunks";
import {
  setImageHyperRows,
  updateExtraviewImageSubtype,
  updateExtraviewImageType,
  updateExtraViews,
  updateLoggingBarSuite,
  updateMeasurePointsInterval,
  updateRecoveries,
  updateRqdCalculationResult,
  updateSelectedDrilhole,
  updateSelectedRockLineType,
} from "../redux/loggingSlice";
import {
  getExtraViewModeImages,
  getLoggingBar,
} from "../redux/loggingSlice/thunks";
import { selectLoggingBarData } from "../redux/loggingSlice/selectors";

// API services
import imageRequest from "@/modules/image/api/image.api";
import recoveryRequest from "../api/recovery.api";

// Builder pattern for logging view stacks

// Types and interfaces
import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { StructureSelectorType } from "@/modules/structure-type/enum/enum";
import { DataEntryValueBody } from "../api/data-entry.api";
import { EnumLoggingExtraViews } from "../model/enum/logging.enum";
import {
  LoggingImageProps,
  OCRResultItem,
  RockLineType,
} from "../types/logging.types";

// Utility functions
import {
  parseTreeNodeValue,
  transformImageTypeDataToTreeNodes,
} from "../utils/logging.utils";

// Constants
import {
  ROCK_LINE_OPTIONS,
  VIEW_MODE_OPTIONS,
} from "../constants/logging.constants";

// Components
import { ModalOcrList } from "@/modules/process-images/components/ocr/modal-ocr-list";
import LoggingStage from "./logging-stage";

import dhCalculationRequest from "@/modules/dh-calculation/api/dh-calculation.api";
import geologySuiteRequest from "@/modules/geology-suite/api/geology-suite.api";
import { updateGeologySuiteBar } from "../redux/loggingSlice/logging.slice";
import { LoggingViewStackBuilder } from "../utils/logging-view-stack-builder.util";

function LoggingImage({
  imageRows,
  viewModes,
  setViewModes,
  directOCRdata,
  setDirectOCRdata,
  image,
  directOCRdataRaw,
  getCurrentImage,
  setCurrentImage,
  selectedImageType,
  selectedImageSubtype,
  refetchDataEntry,
  modalStateEntryData,
  setModalStateEntryData,
}: LoggingImageProps) {
  const dispatch = useAppDispatch();
  const queries: any = {};
  const searchParams = useSearchParams();

  for (const [key, value] of searchParams.entries()) {
    const arrayValues = searchParams.getAll(key);
    queries[key] = arrayValues.length > 1 ? arrayValues : value;
  }
  const params = new URLSearchParams(queries);
  const { data: geologySuite } = useQueryGeologySuite();
  const geologySuiteOptions = geologySuite?.data?.items?.map((item: any) => ({
    label: item.name,
    value: item.id,
  }));
  const [rockGroupFieldIds, setRockGroupFieldIds] = useState<string[]>([]);
  const [rockGroupOptions, setRockGroupOptions] = useState<any[]>([]);
  const [filteredDataEntry, setFilteredDataEntry] = useState<any[]>([]);
  const { request: requestGetGeologySuite } = useGetGeologySuite();
  const { request: requestGetDataEntry, data: dataEntry } =
    useGetListDataEntry();

  // Redux selectors for logging bar data
  const loggingBarData = useAppSelector(selectLoggingBarData);
  const loggingBarSuite = useAppSelector(
    (state) => state.logging.loggingBarSuite
  );

  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );

  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo?.prospectId
  );
  const selectedDrillhole = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );
  const structure = useAppSelector((state) => state.structure?.detail);

  const selectedImageSubTypeId = useAppSelector(
    (state) => state.logging.selectedImageSubtypeId
  );

  const imageDepthFrom = useAppSelector((state) => state.logging.depthFrom);
  const imageDepthTo = useAppSelector((state) => state.logging.depthTo);
  const projectDetail = useAppSelector((state) => state.project.detail);
  const skipCount = useAppSelector((state) => state.logging.skipCount);

  const selectedRockLineType = useAppSelector(
    (state) => state.logging.selectedRockLineType
  );
  const loggingSuiteMode = useAppSelector(
    (state) => state.logging.loggingSuiteMode
  );
  const measurePointsInterval = useAppSelector(
    (state) => state.logging.measurePointsInterval
  );
  const imageExtraRows = useAppSelector(
    (state) => state?.logging?.imageExtraRows
  );
  const imageGap = useAppSelector((state) => state.logging.imageGap);

  const extraViews = useAppSelector((state) => state?.logging?.extraViews);
  const selectedDrillHole = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );
  const isShowSegmentation = useAppSelector(
    (state) => state.logging.isShowSegmentation
  );
  const isInterval = structure?.selector === StructureSelectorType.Interval;

  const [selectedAttribute, setSelectedAttribute] = useState<string[]>();
  const [selectedGeophysic, setSelectedGeophysic] = useState<string>();
  const [isOpenModalOCRList, setIsOpenModalOCRList] = useState(false);
  const [imageTypeValue, setImageTypeValue] = useState<string | undefined>();

  // Pixel color picker state
  const isPixelColorPickerEnabled = useAppSelector(
    (state) => state.logging.isPixelColorPickerEnabled
  );

  const {
    data: imageTypeData,
    isLoading: isFetchingImageType,
    searchParams: imageTypeSearchParams,
    setSearchParams: setImageTypeSearchParams,
  } = useQueryImageType();

  useEffect(() => {
    if (globalProjectId) {
      setImageTypeSearchParams({
        ...imageTypeSearchParams,
        projectId: globalProjectId,
      });
    }
  }, [globalProjectId]);

  const imageTypeTreeData = useMemo(
    () => transformImageTypeDataToTreeNodes(imageTypeData?.data?.items),
    [imageTypeData]
  );

  const handleImageTypeChange = (value: string) => {
    setImageTypeValue(value);
    const { typeId, subTypeId } = parseTreeNodeValue(value);

    dispatch(updateExtraviewImageType(typeId));
    dispatch(updateExtraviewImageSubtype(subTypeId));

    if (
      (value && viewModes.includes(EnumLoggingExtraViews.Below)) ||
      viewModes.includes(EnumLoggingExtraViews.Overlay)
    ) {
      dispatch(
        getExtraViewModeImages({
          imageSubtypeId: subTypeId,
          imageTypeId: typeId,
          projectIds: [globalProjectId],
          prospectIds: [globalProspectId],
          holeIds: [selectedDrillhole?.value],
          depthFrom: imageDepthFrom,
          depthTo: parseFloat(imageDepthTo.toFixed(2)),
        })
      );
      dispatch(updateExtraViews(viewModes as EnumLoggingExtraViews[]));
    } else {
      dispatch(updateExtraViews([]));
      dispatch(setImageHyperRows([]));
    }
  };

  const handleViewModeChange = (selectedViewModes: EnumLoggingExtraViews[]) => {
    setViewModes(selectedViewModes);

    if (!imageTypeValue) return;

    // Clear extra views if no view modes selected or no image type
    const hasValidViewModes =
      selectedViewModes.includes(EnumLoggingExtraViews.Below) ||
      selectedViewModes.includes(EnumLoggingExtraViews.Overlay);

    if (!hasValidViewModes) {
      dispatch(updateExtraViews([]));
      dispatch(setImageHyperRows([]));
      return;
    }

    const { typeId, subTypeId } = parseTreeNodeValue(imageTypeValue);

    dispatch(
      getExtraViewModeImages({
        imageSubtypeId: subTypeId,
        imageTypeId: typeId,
        projectIds: [globalProjectId],
        prospectIds: [globalProspectId],
        holeIds: [selectedDrillhole?.value],
        depthFrom: imageDepthFrom,
        depthTo: parseFloat(imageDepthTo.toFixed(2)),
      })
    );
    dispatch(updateExtraViews(selectedViewModes));
  };

  const drillholeIdParams = params.get("drillholeId");

  const {
    data: geophysicsAttributes,
    request: requestGetListAttributeBySuiteId,
  } = useGetListAttributeBySuiteId();

  const handleAttributeSelection = (selectedAttributes: string[]) => {
    setSelectedAttribute(selectedAttributes);
  };

  const handleRockLineTypeChange = (selectedRockLineType: RockLineType) => {
    dispatch(updateSelectedRockLineType(selectedRockLineType));

    if (selectedRockLineType === RockLineType.RQD) {
      const drillholeId = drillholeIdParams ?? selectedDrillHole?.value;
      if (!drillholeId) return;

      dhCalculationRequest
        .getRqdCalculationResultByDrillHole({
          drillHoleId: Number(drillholeId),
          maxResultCount: 1000,
        })
        .then((res) => {
          if (res.state === RequestState.success) {
            dispatch(updateRqdCalculationResult(res.data?.items));
          }
        });
    }
  };

  const handleGeophysicsSelection = (selectedGeophysicsId: string) => {
    setSelectedGeophysic(selectedGeophysicsId);

    if (selectedGeophysicsId) {
      requestGetListAttributeBySuiteId({
        Id: selectedGeophysicsId,
      });
    } else {
      setSelectedAttribute(undefined);
    }
  };

  const { request: requestGetDownholeByProject, data: dataDownholes } =
    useGetDownholeByProject();

  // OCR text update handler - saves changes to backend
  const handleOCRTextUpdate = (ocrItem: OCRResultItem, newText: string) => {
    const updatedOCRData = directOCRdata.map((item) => {
      if (item.id === ocrItem.id) {
        return {
          ...item,
          text: newText,
        };
      }
      return item;
    });

    setDirectOCRdata(updatedOCRData);

    imageRequest
      .updateResultOCR({
        id: image?.id,
        ocr: JSON.stringify(updatedOCRData),
      })
      .then(() => {
        toast.success("OCR text updated successfully", {
          position: "top-center",
        });
      })
      .catch(() => {
        toast.error("Failed to update OCR text", {
          position: "top-center",
        });
      });
  };

  // OCR deletion handler
  const handleOCRDeletion = (ocrId: string | number) => {
    const updatedOCRData = directOCRdata.filter((item) => item.id !== ocrId);

    imageRequest
      .updateResultOCR({
        id: image?.id,
        ocr: JSON.stringify(updatedOCRData),
      })
      .then(() => {
        setDirectOCRdata(updatedOCRData);
      })
      .catch(() => {
        toast.error("Failed to delete OCR item", {
          position: "top-center",
        });
      });
  };

  // Handle double-click to enable OCR text editing
  const onDblClickText = (data: any) => {
    const updatedOCRData = directOCRdata.map((item) => ({
      ...item,
      isEdit: item.id === data.id,
    }));
    setDirectOCRdata(updatedOCRData);
  };

  // Handle OCR annotation changes (position, size, etc.)
  const handleOCRAnnotationChange = (updatedAttributes: any) => {
    const updatedOCRData = directOCRdata.map((item) => {
      if (item.id === updatedAttributes.id) {
        return updatedAttributes;
      }
      return item;
    });
    setDirectOCRdata(updatedOCRData);
  };

  // Handle OCR text change during editing (draft state)
  const onChangeText = (data: any, value: string) => {
    const updatedOCRData = directOCRdata.map((item) => {
      if (item.id === data.id) {
        return {
          ...item,
          draftText: Number(value),
        };
      }
      return item;
    });
    setDirectOCRdata(updatedOCRData);
  };

  // Refresh image data and update current image
  const refreshImageData = () => {
    const drillholeId = drillholeIdParams ?? selectedDrillHole?.value;
    const imageSkipCount = skipCount ?? 0;

    if (!drillholeId) return;

    // Update selected drillhole in Redux state
    dispatch(
      updateSelectedDrilhole({
        value: selectedDrillhole?.value,
        label: selectedDrillhole?.label,
      })
    );

    // Fetch new image data
    getCurrentImage(
      Number(drillholeId),
      imageSkipCount + 1,
      selectedImageType,
      selectedImageSubtype
    );

    // Update current image index
    setCurrentImage(imageSkipCount + 1);

    // Fetch drillhole details
    dispatch(getDetailDrillhole(Number(drillholeId)));
  };

  // Refresh logging bar data if geology suite is selected
  const refreshLoggingBarData = () => {
    if (!loggingBarSuite || !selectedDrillhole?.value) return;

    const loggingBarParams: any = {
      GeologySuiteId: Number(loggingBarSuite),
      DrillholeId: Number(selectedDrillhole.value),
      SkipCount: 0,
      MaxResultCount: 1000,
    };

    // Include image subtype if available
    if (selectedImageSubTypeId) {
      loggingBarParams.ImageSubtypeId = selectedImageSubTypeId;
    }

    dispatch(getLoggingBar(loggingBarParams));
  };

  const loggingViewStacks = useMemo(() => {
    // Use Builder pattern to construct logging view stacks systematically
    const builder = new LoggingViewStackBuilder(
      imageRows,
      imageGap,
      imageExtraRows,
      extraViews,
      loggingBarData,
      selectedImageSubTypeId?.toString() || "",
      rockGroupFieldIds,
      filteredDataEntry,
      selectedAttribute || [],
      dataDownholes
    );

    return builder.build();
  }, [
    imageRows,
    dataDownholes,
    selectedAttribute,
    imageExtraRows,
    extraViews,
    imageGap,
    rockGroupFieldIds,
    filteredDataEntry,
    loggingBarData,
    selectedImageSubTypeId,
  ]);
  console.log("loggingViewStacks: ", loggingViewStacks);

  // Fetch recovery data for segmentation display
  const fetchRecoveries = async () => {
    if (!isShowSegmentation || !selectedDrillhole?.value) return;

    try {
      const result = await recoveryRequest.getRecoveryByDrillHole({
        drillHoleId: Number(selectedDrillhole.value),
      });

      if (result.state === RequestState.success && result.data?.items) {
        dispatch(updateRecoveries(result.data.items));
      }
    } catch (error) {
      toast.error("Failed to fetch recovery data");
    }
  };

  useEffect(() => {
    fetchRecoveries();
  }, [isShowSegmentation, selectedDrillhole]);

  // Handle geology suite selection and reset dependent data
  const handleGeologySuiteSelection = async (selectedSuiteId: string) => {
    if (selectedSuiteId) {
      const res = await geologySuiteRequest.getDetail(selectedSuiteId);
      if (res.state === RequestState.success) {
        dispatch(updateGeologySuiteBar(res.data));
      }
    }
    dispatch(updateLoggingBarSuite(selectedSuiteId));
    // Clear dependent selections when geology suite changes
    setRockGroupFieldIds([]);
    setFilteredDataEntry([]);
  };

  // Handle rock group selection
  const handleRockGroupSelection = (selectedRockGroupIds: string[]) => {
    setRockGroupFieldIds(selectedRockGroupIds);
  };

  // Effect: Handle geology suite selection and fetch related data
  useEffect(() => {
    if (!loggingBarSuite) return;

    requestGetGeologySuite(loggingBarSuite, (geologySuiteResponse: any) => {
      // Extract rock group fields from geology suite
      const rockGroupFields = geologySuiteResponse?.geologySuiteFields?.filter(
        (field: any) => field?.geologyField?.type === FieldType.RockGroup
      );

      // Set rock group options for selection

      setRockGroupOptions(
        rockGroupFields?.map((field: any) => ({
          label: field.name,
          value: field.id,
        })) || []
      );

      // Fetch data entry for the selected geology suite and drillhole
      if (selectedDrillhole?.value) {
        requestGetDataEntry({
          GeologySuiteId: Number(loggingBarSuite),
          DrillholeId: Number(selectedDrillhole.value),
          skipCount: 0,
          maxResultCount: 1000,
        });

        // Fetch logging bar data
        const loggingBarParams: any = {
          GeologySuiteId: Number(loggingBarSuite),
          DrillholeId: Number(selectedDrillhole.value),
          SkipCount: 0,
          MaxResultCount: 1000,
        };

        // Include image subtype if available
        if (selectedImageSubTypeId) {
          loggingBarParams.ImageSubtypeId = selectedImageSubTypeId;
        }

        dispatch(getLoggingBar(loggingBarParams));
      }
    });
  }, [loggingBarSuite, selectedImageSubTypeId, selectedDrillhole?.value]);

  // Effect: Fetch logging bars when drillhole changes (if geology suite is already selected)
  // Note: This effect is redundant with the previous one, but kept for explicit drillhole changes
  useEffect(() => {
    if (!loggingBarSuite || !selectedDrillhole?.value) return;

    const loggingBarParams: any = {
      GeologySuiteId: Number(loggingBarSuite),
      DrillholeId: Number(selectedDrillhole.value),
      SkipCount: 0,
      MaxResultCount: 1000,
    };

    // Include image subtype if available
    if (selectedImageSubTypeId) {
      loggingBarParams.ImageSubtypeId = selectedImageSubTypeId;
    }

    dispatch(getLoggingBar(loggingBarParams));
  }, [selectedDrillhole?.value, loggingBarSuite, selectedImageSubTypeId]);

  // Effect: Filter data entries based on selected rock group IDs
  useEffect(() => {
    if (!dataEntry || rockGroupFieldIds.length === 0) {
      setFilteredDataEntry([]);
      return;
    }

    const filteredEntries: any[] = [];

    dataEntry.forEach((entry: any) => {
      // Check if this entry has any of the selected rock groups
      const hasMatchingRockGroup = entry.dataEntryValues.some(
        (value: DataEntryValueBody) => {
          return (
            value.fieldType === FieldType.RockGroup &&
            rockGroupFieldIds.includes(value.geologysuiteFieldId)
          );
        }
      );

      if (hasMatchingRockGroup) {
        filteredEntries.push({
          id: entry.id,
          depthFrom: entry.depthFrom,
          depthTo: entry.depthTo,
          dataEntryValues: entry.dataEntryValues,
        });
      }
    });

    setFilteredDataEntry(filteredEntries);
  }, [dataEntry, rockGroupFieldIds]);

  // Effect: Fetch downhole data and geology suites when project/drillhole changes
  useEffect(() => {
    if (!globalProjectId || !selectedDrillhole?.label) return;

    // Fetch downhole geophysics data for the selected drillhole
    requestGetDownholeByProject({
      DepthFrom: image?.depthFrom,
      DepthTo: image?.depthTo,
      DrillHoleName: [selectedDrillhole.label],
      projectId: globalProjectId,
    });

    // Fetch available geology suites
    dispatch(getGeologySuite({ isActive: true, maxResultCount: 1000 }));
  }, [
    globalProjectId,
    selectedDrillhole?.label,
    image?.depthFrom,
    image?.depthTo,
  ]);

  // Render empty state when no image rows are available
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center mt-2 w-full">
      <div className="flex flex-col md:items-center justify-center max-w-md p-8 bg-white rounded-lg shadow-md">
        <img
          src="/images/ic_notResult.png"
          alt="No cropped images"
          className="md:w-40 md:h-40 w-20 h-20 mb-6 opacity-80"
        />
        <h2 className="text-2xl font-bold text-gray-700 mb-2">
          No cropped images available
        </h2>
        <p className="text-gray-500 text-center mb-6">
          There are no cropped images available.
        </p>
        <div className="flex items-center justify-center">
          <div className="w-3 h-3 bg-amber-500 rounded-full animate-ping mr-2"></div>
          <span className="text-amber-500 font-medium">
            Try selecting a different drill hole
          </span>
        </div>
      </div>
    </div>
  );

  if (isEmpty(imageRows)) {
    return renderEmptyState();
  }
  return (
    <Fragment>
      <div className="">
        {/* Control Panel */}
        <div className="flex flex-col gap-1 mt-1 md:relative">
          <div className="flex items-center justify-between gap-8 w-full pb-1">
            <div className="flex gap-3 items-center flex-wrap w-full">
              {/* Image Type and View Mode Controls */}
              <div className="flex items-center gap-3">
                <div className="items-center flex gap-2">
                  <p className="font-medium">Image</p>
                  <TreeSelect
                    placeholder="Select image type"
                    treeData={imageTypeTreeData}
                    value={imageTypeValue}
                    onChange={handleImageTypeChange}
                    loading={isFetchingImageType}
                    allowClear
                    treeDefaultExpandAll
                    dropdownStyle={{
                      maxHeight: 400,
                      overflow: "auto",
                      minWidth: "min(300px, 90vw)",
                      maxWidth: "min(600px, 95vw)",
                    }}
                  />
                  {imageTypeValue && (
                    <Select
                      mode="multiple"
                      placeholder="View mode"
                      options={VIEW_MODE_OPTIONS}
                      value={viewModes}
                      onChange={handleViewModeChange}
                      className="min-w-28"
                    />
                  )}
                </div>
                {/* Geophysics Controls */}
                <div className="items-center flex gap-2">
                  <p className="font-medium">Geophysics</p>
                  <Select
                    placeholder="Choose geophysics"
                    options={projectDetail?.geophysicsSuites?.map(
                      (geophysics: any) => ({
                        label: geophysics.name,
                        value: geophysics.id,
                      })
                    )}
                    allowClear
                    value={selectedGeophysic}
                    className="min-w-24"
                    onChange={handleGeophysicsSelection}
                  />
                  {selectedGeophysic && (
                    <Select
                      placeholder="Choose attributes"
                      options={geophysicsAttributes.map((attribute) => ({
                        label: attribute.name,
                        value: attribute.name,
                      }))}
                      allowClear
                      mode="multiple"
                      className="min-w-36"
                      onChange={handleAttributeSelection}
                    />
                  )}
                </div>

                {/* Rock Line Controls */}
                <div className="items-center flex gap-2">
                  <p className="font-medium">Rock Line</p>
                  <Select
                    placeholder="Select rock line type"
                    options={ROCK_LINE_OPTIONS}
                    value={selectedRockLineType}
                    onChange={handleRockLineTypeChange}
                    defaultValue={RockLineType.Recovery}
                  />
                </div>

                {/* Geology Suite Controls */}
                <div className="items-center flex gap-2">
                  <p className="font-medium">Geology Bars</p>
                  <Select
                    placeholder="Select suite"
                    options={geologySuiteOptions}
                    allowClear
                    value={loggingBarSuite}
                    onChange={handleGeologySuiteSelection}
                  />
                  {/* Rock Group Controls */}
                  {loggingBarSuite && (
                    <Select
                      placeholder="Select rock group"
                      options={rockGroupOptions}
                      allowClear
                      mode="multiple"
                      value={rockGroupFieldIds}
                      onChange={handleRockGroupSelection}
                      className="min-w-[170px]"
                    />
                  )}
                </div>
              </div>

              {/* Geotech Interval Display */}
              {loggingSuiteMode === "Geotech" && isInterval && (
                <div className="font-medium">
                  From{" "}
                  <Tag color="blue">
                    {measurePointsInterval?.start?.depth.toFixed(2)}{" "}
                  </Tag>
                  to{" "}
                  <Tag color="gold">
                    {measurePointsInterval?.end?.depth.toFixed(2)}
                  </Tag>
                  <Button
                    type="primary"
                    size="small"
                    disabled={
                      !measurePointsInterval?.start &&
                      !measurePointsInterval?.end
                    }
                    onClick={() => {
                      dispatch(updateMeasurePointsInterval({}));
                    }}
                  >
                    Reset
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Logging Stage Component */}
        <LoggingStage
          directOCRdata={directOCRdata}
          setDirectOCRdata={setDirectOCRdata}
          loggingViewStacks={loggingViewStacks}
          onChangeText={onChangeText}
          onEnterChangeText={handleOCRTextUpdate}
          onDblClickText={onDblClickText}
          onChange={handleOCRAnnotationChange}
          directOCRdataRaw={directOCRdataRaw}
          image={image}
          setIsOpenModalOCRList={setIsOpenModalOCRList}
          refreshImageData={refreshImageData}
          fetchRecoveries={fetchRecoveries}
          onDeleteOcr={handleOCRDeletion}
          refreshLoggingBarData={refreshLoggingBarData}
          refetchDataEntry={refetchDataEntry}
          modalStateEntryData={modalStateEntryData}
          setModalStateEntryData={setModalStateEntryData}
          isPixelColorPickerEnabled={isPixelColorPickerEnabled}
        />
      </div>

      {/* OCR List Modal */}
      {isOpenModalOCRList && (
        <ModalOcrList
          OCRdata={directOCRdata}
          isOpenModalOCRList={isOpenModalOCRList}
          setIsOpenModalOCRList={setIsOpenModalOCRList}
          setOCRdata={setDirectOCRdata}
          image={image}
          directOCRdata={directOCRdata}
          setDirectOCRdata={setDirectOCRdata}
        />
      )}
    </Fragment>
  );
}

export default LoggingImage;
