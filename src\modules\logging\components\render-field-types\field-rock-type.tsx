import { InputNumber } from "antd";
import React, { memo, useCallback, useState } from "react";
import { Control, Controller, useWatch } from "react-hook-form";
import { ErrorTooltip } from "./error-tooltip";

interface RockTypeOption {
  id: number;
  name?: string;
  code?: string;
  rockStyle?: {
    fillColor?: string;
  };
}

interface FieldRockTypeProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  options?: RockTypeOption[];
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  // New props for combined functionality
  numberFieldName?: string;
  unit?: string;
  showNumberInput?: boolean;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldRockType = memo<FieldRockTypeProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    options = [],
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    numberFieldName,
    unit,
    showNumberInput = false,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const [searchValue, setSearchValue] = useState("");
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        const arrowKeys = [
          "ArrowUp",
          "ArrowDown",
          "ArrowLeft",
          "ArrowRight",
          "Tab",
        ];

        if (arrowKeys.includes(event.key)) {
          if (event.shiftKey) {
            setDropdownOpen(true);
          } else {
            setDropdownOpen(false);
            event.preventDefault();
          }

          onKeyDown?.(event);
        }
      },
      [onKeyDown]
    );

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    const handleNumberInputFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    // Create sorted options that prioritize entries beginning with search input
    const createSortedOptions = useCallback(
      (searchValue: string = "") => {
        const inputLower = searchValue.toLowerCase();

        return options
          .filter((option) => {
            const nameLower = option.name?.toLowerCase() || "";
            const codeLower = option.code?.toLowerCase() || "";
            return (
              nameLower.includes(inputLower) || codeLower.includes(inputLower)
            );
          })
          .map((option) => ({
            label: (
              <div className="flex items-center gap-2">
                {option.rockStyle?.fillColor && (
                  <div
                    className="min-w-4 h-4 rounded border border-gray-300"
                    style={{ backgroundColor: option.rockStyle.fillColor }}
                  />
                )}
                <span>{option.name}</span>
              </div>
            ),
            value: option.id,
            originalOption: option,
          }))
          .sort((a, b) => {
            const nameA = a.originalOption.name?.toLowerCase() || "";
            const codeA = a.originalOption.code?.toLowerCase() || "";
            const nameB = b.originalOption.name?.toLowerCase() || "";
            const codeB = b.originalOption.code?.toLowerCase() || "";

            // Check if entries start with the search input
            const aStartsWith =
              nameA.startsWith(inputLower) || codeA.startsWith(inputLower);
            const bStartsWith =
              nameB.startsWith(inputLower) || codeB.startsWith(inputLower);

            // Prioritize entries that start with the search input
            if (aStartsWith && !bStartsWith) return -1;
            if (!aStartsWith && bStartsWith) return 1;

            // If both start with or both don't start with, sort alphabetically
            return nameA.localeCompare(nameB) || codeA.localeCompare(codeB);
          });
      },
      [options]
    );

    if (showNumberInput && numberFieldName) {
      const rockValue = useWatch({ control, name });
      const numberValue = useWatch({ control, name: numberFieldName });

      // Combined validation function
      const validateCombinedField = (
        value: any,
        fieldType: "rock" | "number"
      ) => {
        if (!mandatory) return true;

        const hasRockValue =
          rockValue !== null && rockValue !== undefined && rockValue !== "";
        const hasNumberValue =
          numberValue !== null &&
          numberValue !== undefined &&
          numberValue !== "";

        // Both fields are required when mandatory
        if (!hasRockValue || !hasNumberValue) {
          return fieldType === "rock"
            ? "Both rock type and number value are required"
            : "Both rock type and number value are required";
        }
        return true;
      };

      return (
        <Controller
          name={numberFieldName}
          control={control}
          rules={{
            validate: (value) => validateCombinedField(value, "number"),
          }}
          render={({
            field: numberField,
            fieldState: { error: numberError },
          }) => (
            <div className="flex gap-2">
              <InputNumber
                {...numberField}
                id={id ? `${id}-number` : undefined}
                disabled={disabled}
                placeholder="Value"
                className="flex-1 w-full"
                onKeyDown={handleKeyDown}
                onFocus={handleNumberInputFocus}
                onBlur={handleBlur}
                onChange={(value) => {
                  numberField.onChange(value);

                  // Trigger row status update for number field
                  if (
                    onFieldChange &&
                    typeof rowIndex === "number" &&
                    numberFieldName
                  ) {
                    onFieldChange(rowIndex, numberFieldName, value);
                  }
                }}
              />
              {unit && (
                <span className="text-sm text-gray-600 whitespace-nowrap">
                  {unit}
                </span>
              )}
              <ErrorTooltip error={numberError} />
            </div>
          )}
        />
      );
    }
  }
);

FieldRockType.displayName = "FieldRockType";
