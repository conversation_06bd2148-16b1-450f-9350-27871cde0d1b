import { useState } from "react";
import dataEntryRequest, { LoggingBarItem } from "../api/data-entry.api";

export const useGetLoggingBar = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<LoggingBarItem[]>([]);
  
  const request = async (
    params: {
      GeologySuiteId: number;
      DrillholeId: number;
      ImageSubtypeId?: number;
      SkipCount?: number;
      MaxResultCount?: number;
    },
    onSuccess?: Function,
    onError?: Function,
    forceUpdate?: boolean
  ) => {
    setLoading(true);
    const response = await dataEntryRequest.getAllLoggingBar({
      ...params,
    });
    if (response?.state === "success") {
      setData(response.data?.items || []);
      setTotal(response.data?.totalCount || 0);
      setLoading(false);
      onSuccess && onSuccess(response.data);
      return response.data;
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
