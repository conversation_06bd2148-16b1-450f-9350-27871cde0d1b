import z from "zod";

export const ImageTypeBody = z.object({
  id: z.any().optional(),
  name: z.string().trim().min(1, { message: "Required" }),
  isActive: z.boolean(),
  isStandard: z.boolean().optional(),
  isRigCorrected: z.boolean().optional(),
  isRig: z.boolean().optional(),
  // sequence: z.number().optional().nullable(),
  priority: z.number().nullable(),
  projectIds: z.array(z.any()).optional(),
});

export type ImageTypeBodyType = z.TypeOf<typeof ImageTypeBody>;

export const ImageSubTypeBody = z.object({
  id: z.any().optional(),
  name: z.string().trim().min(1, { message: "Required" }),
  imageTypeId: z.any(),
  isActive: z.boolean(),
  isWet: z.boolean().optional(),
  isDry: z.boolean().optional(),
  isUv: z.boolean().optional(),
  sequence: z.number().nullable(),
});

export type ImageSubTypeBodyType = z.TypeOf<typeof ImageSubTypeBody>;
