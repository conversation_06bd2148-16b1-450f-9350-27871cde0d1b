"use client";
import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useQueryDownholeSurveyType } from "@/modules/downhole-survey-type/hooks/useQueryDownholeSurveyType";
import geologySuiteRequest from "@/modules/geology-suite/api/geology-suite.api";
import { useGetDetailProject } from "@/modules/projects/hooks/useGetDetailProject";
import { useGetDetailProspect } from "@/modules/prospect/hooks/useGetDetailProspect";
import { getProjects } from "@/modules/projects/redux/projectSlice/thunks";
import { InboxOutlined, UploadOutlined } from "@ant-design/icons";
import { Button, Form, message, Upload } from "antd";
import { useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { toast } from "react-toastify";
import * as XLSX from "xlsx";
import importDataRequest, {
  ImportFileType,
  ImportMappingType,
  ImportMode,
  ImportModeOptions,
} from "../api/importData.api";
import { useGetListTemplate } from "../hooks/useGetListTemplate";
import {
  setField,
  setFileName,
  setGeologySuite,
  setHeader,
  setTemplateSelected,
} from "../redux/importDataSlice/import-data.slice";
import { FormTable } from "./form-table";
import { ModalMapping } from "./modal-mapping";
import { ModalTemplate } from "./modal-template";
const { Dragger } = Upload;

interface DataType {
  [key: string]: any;
}

const ImportData: React.FC = () => {
  const { header, fileName, field, templateSelected } = useAppSelector(
    (state) => state.importData
  );

  const handleUpload = (file: File) => {
    // Reset form and states when new file is uploaded
    form.reset();
    setErrorImport([]);
    setOverlapError(new Set());
    setGapError(new Set());
    dispatch(setFileName(file.name));
    dispatch(setHeader([]));
    dispatch(setField([]));
    dispatch(setTemplateSelected(undefined));
    setValue("ImportMappingTemplateId", undefined);
    setValue("ImportMappingFields", undefined);

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const workbook = XLSX.read(e.target?.result, { type: "binary" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Get cell types and values
        const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");
        const cellTypes = new Map();

        for (let R = range.s.r; R <= range.e.r; R++) {
          for (let C = range.s.c; C <= range.e.c; C++) {
            const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
            const cell = worksheet[cellAddress];
            if (cell) {
              cellTypes.set(cellAddress, {
                t: cell.t, // cell type
                v: cell.v, // cell value
              });
            }
          }
        }

        const _jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          raw: false,
          dateNF: "yyyy/mm/dd",
        }) as any[][];

        let headers = _jsonData[0] as string[];
        const rows = _jsonData.slice(1);
        const _data = rows.map((row, index) => {
          const rowData: DataType = { key: index.toString() };
          headers.forEach((header, colIndex) => {
            const cellAddress = XLSX.utils.encode_cell({
              r: index + 1,
              c: colIndex,
            });
            const cellInfo = cellTypes.get(cellAddress);

            if (cellInfo && cellInfo.t === "d") {
              // If cell is a date, format it as yyyy/mm/dd
              rowData[header] = cellInfo.v
                ? new Date(cellInfo.v)
                    .toISOString()
                    .split("T")[0]
                    .replace(/-/g, "/")
                : "";
            } else {
              rowData[header] = row[colIndex] ?? "";
            }
          });
          return rowData;
        });

        // Add validation after setting data
        setValue("dataUpload", _data);
        dispatch(setHeader(headers));
      } catch (error) {
        message.error("Error reading file");
        console.error(error);
      }
    };
    reader.readAsBinaryString(file);
    return false;
  };

  const handleDownload = () => {
    if (dataTable.length === 0) {
      message.warning("No data to download");
      return;
    }

    // Remove key from data before downloading
    const dataToDownload = dataTable.map(({ key, ...rest }) => rest);
    const worksheet = XLSX.utils.json_to_sheet(dataToDownload);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
    XLSX.writeFile(workbook, "edited_data.xlsx");
  };

  const [isUploading, setIsUploading] = useState(false);
  const [isIgnoreGaps, setIsIgnoreGaps] = useState(false);
  const dispatch = useAppDispatch();
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state) => state.user?.userInfo?.prospectId
  );

  const form = useForm<any>({
    defaultValues: {
      projectId: globalProjectId,
      prospectId: globalProspectId,
      ExcelFile: undefined,
      ImportMode: ImportModeOptions[0].value,
      CreateNewDrillHoles: false,
      dataUpload: [],
    },
    mode: "onSubmit",
    reValidateMode: "onSubmit",
    criteriaMode: "all",
  });
  const { handleSubmit, control, getValues, watch, setValue } = form;
  const fieldArray = useFieldArray({
    control,
    name: "dataUpload",
  });

  const [errorImport, setErrorImport] = useState<any[]>([]);
  const dataTable = watch("dataUpload");
  const [overlapError, setOverlapError] = useState<Set<[number, number]>>(
    new Set()
  );
  const [gapError, setGapError] = useState<Set<[number, number]>>(new Set());

  const handleUploadData = async (payload: any) => {
    if (!payload?.dataUpload?.length) {
      toast.error("Please upload a file");
      return;
    }
    if (!payload?.ImportFileType) {
      toast.error("Please select import file type");
      return;
    }

    if (
      !payload?.projectId &&
      !payload?.prospectId &&
      payload.ImportFileType === ImportMappingType.DrillHole
    ) {
      toast.error("Please select project and prospect");
      return;
    }

    setIsUploading(true);
    try {
      // Create a new workbook and worksheet from the data
      const worksheet = XLSX.utils.json_to_sheet(
        dataTable.map(({ key, ...rest }) => rest)
      );
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      // Convert workbook to blob
      const wbout = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
      const blob = new Blob([wbout], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // Create File object from blob
      const editedFile = new File([blob], fileName || "edited_data.xlsx", {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      const response = await importDataRequest.importData({
        ExcelFile: editedFile,
        projectId: payload.projectId as any,
        CreateNewDrillHoles: payload.CreateNewDrillHoles as any,
        ImportMappingTemplateId: payload.ImportMappingTemplateId as any,
        ImportMappingType: payload.ImportMappingType as any,
        ImportMode: payload.ImportMode as any,
        prospectId: payload.prospectId as any,
        ImportFileType: payload.ImportFileType as any,
        ImportMappingFields: payload.ImportMappingFields,
        SuiteId: payload.suiteId as any,
        DownholeSurveyTypeId: payload.downholeSurveyTypeId as any,
      });

      if (response.state === RequestState.error) {
        if (Array.isArray(response.message)) {
          setErrorImport(response.message);
          toast.error(
            `Error import data. See ${response.message?.length} errors`
          );
        } else {
          toast.error(
            response?.message ?? "An error occurred while importing data"
          );
        }
        return;
      }
      if (response.state === RequestState.success) {
        toast.success(
          `Import data successfully! ${response.data.countRecord} rows imported, ${response.data.newDrillHoles?.length} drillholes created`
        );
        setErrorImport([]);
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("An error occurred during upload");
    } finally {
      setIsUploading(false);
      setIsIgnoreGaps(false);
    }
  };

  const handleDrillHoleConfirm = async () => {
    const payload = getValues();
    setIsOpenDrillHoleConfirmModal(false);
    handleUploadData(payload);
  };
  const onSubmit = async (payload: any) => {
    if (!payload) return;

    // Check if it's a drill hole upload and show confirmation modal
    if (payload.ImportFileType === ImportMappingType.DrillHole) {
      setIsOpenDrillHoleConfirmModal(true);
      return;
    }

    const errorOverlap = new Set<[number, number]>();
    const errorGap = new Set<[number, number]>();
    const depthFromField = field.find(
      (d) => d.systemFieldName === "Depth From"
    );
    const depthToField = field.find((d) => d.systemFieldName === "Depth To");
    const fileColumnNameDepthFrom = depthFromField?.fileColumnName;
    const fileColumnNameDepthTo = depthToField?.fileColumnName;
    const sortedRanges = [...payload?.dataUpload].sort(
      (a, b) => a[fileColumnNameDepthFrom] - b[fileColumnNameDepthFrom]
    );

    for (let i = 1; i < sortedRanges.length; i++) {
      const prev = sortedRanges[i - 1];
      const current = sortedRanges[i];

      if (prev[fileColumnNameDepthTo] < current[fileColumnNameDepthFrom]) {
        errorGap.add([
          prev[fileColumnNameDepthTo],
          current[fileColumnNameDepthFrom],
        ]);
        continue;
      }

      if (prev[fileColumnNameDepthTo] > current[fileColumnNameDepthFrom]) {
        errorOverlap.add([
          current[fileColumnNameDepthFrom],
          prev[fileColumnNameDepthTo],
        ]);
        continue;
      }
    }

    setOverlapError(errorOverlap);
    setGapError(errorGap);

    if (errorOverlap.size > 0) {
      toast.error("Please fix the overlap errors before uploading");
      return;
    }

    if (errorGap.size > 0 && !isIgnoreGaps) {
      setIsOpenModalGapWarning(true);
      return;
    }

    handleUploadData(payload);
  };

  useEffect(() => {
    dispatch(
      getProjects({
        page: 1,
        pageSize: 30,
      })
    );
  }, []);

  const [isOpenModal, setIsOpenModal] = useState(false);
  const handleCancel = () => {
    setIsOpenModal(false);
  };

  //get detail project
  const selectedProjectId = watch("projectId");
  const { data: detailProject, request: requestGetDetailProject } =
    useGetDetailProject();
  useEffect(() => {
    if (selectedProjectId) {
      requestGetDetailProject(selectedProjectId as any);
    }
  }, [selectedProjectId]);

  //get detail prospect
  const selectedProspectId = watch("prospectId");
  const { data: detailProspect, request: requestGetDetailProspect } =
    useGetDetailProspect();
  useEffect(() => {
    if (selectedProspectId) {
      requestGetDetailProspect(selectedProspectId as any);
    }
  }, [selectedProspectId]);

  const [openModalTemplate, setOpenModalTemplate] = useState(false);

  const { data: listTemplate, request: requestGetListTemplate } =
    useGetListTemplate();

  const refetchTemplate = () => {
    requestGetListTemplate({
      skipCount: 0,
      maxResultCount: 100,
    });
  };

  useEffect(() => {
    refetchTemplate();
  }, []);
  useEffect(() => {
    dispatch(setFileName(""));
    dispatch(setField([]));
  }, []);

  const templateId = watch("ImportMappingTemplateId");
  useEffect(() => {
    if (templateId) {
      importDataRequest
        .getImportTemplate({ Id: templateId as any })
        .then((res) => {
          if (res.state === RequestState.success) {
            dispatch(setTemplateSelected(res.data));
            const suiteId = res.data?.suiteId;
            const importFileType = res.data?.importFileType;
            setValue("ImportFileType", importFileType);

            if (suiteId) {
              geologySuiteRequest.getDetail(suiteId).then((res) => {
                dispatch(setGeologySuite(res?.data));
              });
              setValue("suiteId", suiteId);
            }
            dispatch(setField(res.data?.importMappingTemplateFields));
            setValue(
              "ImportMappingFields",
              JSON.stringify(res.data?.importMappingTemplateFields)
            );
          }
        });
    }
  }, [templateId]);

  const importFileType = watch("ImportFileType");
  const currentImportMode = watch("ImportMode");

  const [openModalMapping, setOpenModalMapping] = useState(false);
  const [isOpenModalGapWarning, setIsOpenModalGapWarning] = useState(false);
  const [isOpenDrillHoleConfirmModal, setIsOpenDrillHoleConfirmModal] =
    useState(false);
  useEffect(() => {
    setValue("projectId", globalProjectId);
  }, [globalProjectId]);
  useEffect(() => {
    setValue("prospectId", globalProspectId);
  }, [globalProspectId]);

  return (
    <div className="flex flex-col gap-5">
      <ModalCommon
        open={isOpenModalGapWarning}
        centered
        padding={0}
        title="Gap Warning"
        onCancel={() => {
          setIsOpenModalGapWarning(false);
          setIsIgnoreGaps(false);
        }}
        onOk={() => {
          setIsOpenModalGapWarning(false);
          setIsIgnoreGaps(true);
          const values = getValues();
          handleUploadData(values);
        }}
        okText="Upload with gaps"
        cancelText="Cancel"
        confirmLoading={isUploading}
      >
        <div className="flex flex-col gap-2">
          <p>There are some gaps in the data:</p>
          <div className="flex flex-col gap-2">
            {Array.from(gapError).map((d, index) => (
              <p key={index}>
                Gap between depth {d[0]} and {d[1]}
              </p>
            ))}
          </div>
        </div>
      </ModalCommon>

      <ModalCommon
        open={isOpenDrillHoleConfirmModal}
        centered
        padding={0}
        title="Loading to"
        onCancel={() => {
          setIsOpenDrillHoleConfirmModal(false);
        }}
        onOk={handleDrillHoleConfirm}
        okText="Confirm"
        cancelText="Cancel"
        confirmLoading={isUploading}
      >
        <div className="flex flex-col gap-3">
          <div className="flex flex-col gap-2">
            <p>
              <strong>Project:</strong> {detailProject?.name || globalProjectId}
            </p>
            <p>
              <strong>Prospect:</strong>{" "}
              {detailProspect?.name || globalProspectId}
            </p>
          </div>

          {/* Import Mode Information */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div className="flex items-start gap-2">
              <div className="text-blue-600 mt-0.5">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="currentColor"
                >
                  <path d="M8 16A8 8 0 1 1 8 0a8 8 0 0 1 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
                </svg>
              </div>
              <div className="text-sm text-blue-800">
                {currentImportMode === ImportMode.Add && (
                  <p>
                    <strong>Note:</strong> If drill holes already exist in other
                    projects, they may not be updated because the current mode
                    is set to Add only.
                  </p>
                )}
                {currentImportMode === ImportMode.Update && (
                  <p>
                    <strong>Note:</strong> Only drill holes that match entries
                    in the upload file will be updated. No new drill holes will
                    be created.
                  </p>
                )}
                {currentImportMode === ImportMode.AddAndUpdate && (
                  <p>
                    <strong>Note:</strong> Existing drill holes will be updated
                    and new drill holes will be created if they don't already
                    exist.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </ModalCommon>

      {openModalTemplate && (
        <ModalTemplate
          open={openModalTemplate}
          setValue={setValue}
          onCancel={() => setOpenModalTemplate(false)}
          refetchTemplate={refetchTemplate}
        />
      )}

      <ModalMapping
        openModalMapping={openModalMapping}
        setOpenModalMapping={setOpenModalMapping}
        setValue={setValue}
        refetchTemplate={refetchTemplate}
        watch={watch}
      />

      <ModalCommon
        open={isOpenModal}
        centered
        padding={0}
        footer={null}
        onCancel={handleCancel}
        style={{ borderRadius: 8 }}
        width={450}
        closable={false}
      >
        Do you want to upload this file?
        <div className="flex">
          <Button>Yes</Button>
        </div>
      </ModalCommon>
      <p className="text-34-34 font-semibold">Import Data</p>
      <hr />

      <Form
        onFinish={handleSubmit(onSubmit, (err) => {
          console.log(err);
        })}
      >
        <Dragger
          accept=".xlsx,.xls,.csv"
          beforeUpload={handleUpload}
          showUploadList={false}
          className="w-full"
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">
            Click or drag file to this area to upload
          </p>
          <p className="ant-upload-hint">Support for a single upload.</p>
        </Dragger>

        <div className="grid grid-cols-3 gap-3 my-2 items-center">
          <div className="flex gap-2">
            <SelectCommon
              className="flex-1"
              horizontal
              name="ImportMappingTemplateId"
              options={(listTemplate ?? []).map((d) => ({
                label: d.name,
                value: d.id,
              }))}
              filterOption={false}
              control={control}
              label="Template"
              placeholder="Select a template"
              allowClear
              onClear={() => {
                setValue("ImportMappingTemplateId", undefined);
                setValue("ImportMappingFields", undefined);
                setTemplateSelected(undefined);
                dispatch(setField([]));
              }}
            />
            <Button
              type="primary"
              onClick={() => {
                if (templateId) {
                  setOpenModalTemplate(true);
                } else {
                  setOpenModalMapping(true);
                }
              }}
            >
              {templateId ? "Edit" : "Add"} Template
            </Button>
          </div>

          <SelectCommon
            horizontal
            name="ImportMode"
            options={ImportModeOptions}
            label="Import Mode"
            placeholder="Select a import mode"
            control={control}
            isRequired
          />

          {importFileType !== ImportMappingType.DrillHole && (
            <ToogleCommon
              control={control}
              name="CreateNewDrillHoles"
              label="Create New Drill Holes"
              defaultValue={false}
            />
          )}

          {/* {templateId && (
            <p>
              See template:{" "}
              <span
                className="font-bold cursor-pointer text-primary"
                onClick={() => {
                  setOpenModalTemplate(true);
                }}
              >
                {templateSelected?.name}
              </span>
            </p>
          )} */}
        </div>
        <Button
          // disabled={
          //   watch("dataUpload")?.length === 0 || !watch("ImportFileType")
          // }
          loading={isUploading}
          type="primary"
          className="w-full p-2"
          htmlType="submit"
          icon={<UploadOutlined />}
        >
          Upload
        </Button>
      </Form>
      <FormTable
        form={form}
        errorImport={errorImport}
        handleDownload={handleDownload}
        gapError={gapError}
        overlapError={overlapError}
        fieldArray={fieldArray}
      />
    </div>
  );
};
export default ImportData;
