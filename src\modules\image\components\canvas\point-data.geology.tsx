import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { isNumber } from "lodash";
import { Group, Rect, Text } from "react-konva";
import { DrillHoleViewStack } from "../../model/entities/drillhole.config";
import { selectDHViewConfig, selectDHViewInfo } from "../../redux/imageSlice";
import TableKonva from "./table.konva";
import { memo } from "react";
import { getContrastingTextColor } from "@/utils/color.utils";

interface Props {
  intervals: DrillHoleViewStack;
}

const IntervalGeologyColumn = ({ intervals }: Props) => {
  console.log("intervals: ", intervals);
  const dHViewConfigs = useAppSelector(selectDHViewConfig);
  const dHViewInfo = useAppSelector(selectDHViewInfo);
  const { selectedGeologyFieldId } = useAppSelector((state) => state.images);

  const tableData = [
    [intervals?.drillhole?.name],
    [intervals?.extraInfo],
    ["Rock Type"],
  ];

  return (
    <Group>
      {!dHViewInfo?.isHideTableInfo && (
        <TableKonva
          startX={intervals.startX}
          startY={
            isNumber(intervals.minHeight)
              ? intervals.minHeight -
                (tableData.length + 1) * dHViewConfigs.cellHeight
              : 0
          }
          data={tableData}
          cellHeight={dHViewConfigs.cellHeight}
          cellWidth={dHViewConfigs.pointDataWidth / 2.3}
          fontSize={dHViewConfigs.fontSize}
          strokeWidth={dHViewConfigs.strokeWidth}
        />
      )}
      {(intervals.data ?? []).map((interval) => {
        if (!selectedGeologyFieldId) return null;

        // Check if selectedGeologyFieldId is a geology suite field ID (number) or legacy field type (RockType enum)
        const rockTypeInfo = interval.dataEntryValues.find((entry) => {
          return entry.geologysuiteFieldId === selectedGeologyFieldId;
        });

        if (
          !isNumber(interval?.depthTo) ||
          !isNumber(interval?.depthFrom) ||
          !rockTypeInfo
        )
          return null;

        return (
          <Group key={intervals?.id}>
            <Rect
              x={intervals.startX}
              y={interval.depthFrom}
              width={intervals.maxWidth}
              height={interval.depthTo - interval.depthFrom}
              fill={rockTypeInfo?.rockType?.rockStyle?.fillColor}
            />
            <Text
              text={rockTypeInfo?.rockType?.code}
              fontSize={dHViewConfigs.fontSize}
              x={
                intervals.startX +
                intervals.maxWidth / 2 -
                (rockTypeInfo?.rockType?.code?.length *
                  dHViewConfigs.fontSize) /
                  2.6
              }
              y={
                interval.depthFrom +
                (interval.depthTo - interval.depthFrom) / 2 -
                dHViewConfigs.fontSize / 2.6
              }
              fill={getContrastingTextColor(
                rockTypeInfo?.rockType?.rockStyle?.fillColor
              )}
            ></Text>
          </Group>
        );
      })}
    </Group>
  );
};

export default memo(IntervalGeologyColumn);
