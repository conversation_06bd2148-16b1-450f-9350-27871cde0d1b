import { useState } from "react";
import { toast } from "react-toastify";
import mobileProfileRequest from "../api/mobile-profile.api";
import { MobileProfileBodyType } from "../model/schema/mobile-profile.schema";

export const useUpdateMobileProfile = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: MobileProfileBodyType,
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading(true);
    const response = await mobileProfileRequest.update(params);
    if (response.state === "success") {
      onSuccess && onSuccess(response.data);
      setLoading(false);
    } else {
      onError && onError(response.message);
      toast.error(response.message);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
