import { ModalType } from "@/common/configs/app.enum";
import { Button<PERSON>ommon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { TextAreaCommon } from "@/components/common/textarea-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "antd";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useCreateMobileProfile } from "../hooks/useCreateMobileProfile";
import { useDeleteMobileProfile } from "../hooks/useDeleteMobileProfile";
import { useUpdateMobileProfile } from "../hooks/useUpdateMobileProfile";
import {
  MobileProfileBody,
  MobileProfileBodyType,
} from "../model/schema/mobile-profile.schema";
import { MobileCog } from "./mobile-cog";

export interface IModalCompanyProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  refresh: () => void;
}

export function ModalRockStyle(props: IModalCompanyProps) {
  const { modalState, setModalState, refresh } = props;
  const {
    request: requestCreateMobileProfile,
    loading: loadingCreateMobileProfile,
  } = useCreateMobileProfile();
  const {
    request: requestUpdateMobileProfile,
    loading: loadingUpdateMobileProfile,
  } = useUpdateMobileProfile();
  const {
    request: requestDeleteMobileProfile,
    loading: loadingDeleteMobileProfile,
  } = useDeleteMobileProfile();
  const { control, handleSubmit, setValue, getValues, watch } =
    useForm<MobileProfileBodyType>({
      resolver: zodResolver(MobileProfileBody),
      defaultValues: {
        ...modalState?.detailInfo,
        isActive: modalState?.detailInfo?.isActive ?? true,
        isStandard: modalState?.detailInfo?.isStandard ?? false,
        isRig: modalState?.detailInfo?.isRig ?? true,
      },
    });

  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isConfirm = modalState.type === ModalType.DELETE;
  const isCog = modalState.type === ModalType.COG;
  const onSubmit = (values: MobileProfileBodyType) => {
    if (modalState.type === ModalType.CREATE) {
      requestCreateMobileProfile(values, () => {
        setModalState({ ...modalState, isOpen: false });
        toast.success("Create rock style successfully");
        refresh();
      });
    }
    if (modalState.type === ModalType.UPDATE) {
      requestUpdateMobileProfile(
        {
          ...values,
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update drawing style successfully");
          refresh();
        }
      );
    }
  };

  const handleDelete = () => {
    requestDeleteMobileProfile(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refresh();
      },
      (err) => {
        toast.error(err);
      }
    );
  };

  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this mobile profile?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the
            mobile profile
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteMobileProfile}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : isCog ? (
        <MobileCog
          refresh={refresh}
          mobileProfile={modalState.detailInfo}
          setModalState={setModalState}
        />
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === ModalType.UPDATE
              ? "Update mobile profile"
              : "Add mobile profile"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit, (err) => {
              console.log(err);
            })}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              name="name"
              placeholder="Type prospect name here"
              control={control}
              isRequired={true}
            />
            <TextAreaCommon
              label="Description"
              name="description"
              placeholder="Type description here"
              control={control}
            />
            <ToogleCommon control={control} name="isActive" label="Is Active" />

            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                type="submit"
                loading={
                  loadingCreateMobileProfile ||
                  loadingUpdateMobileProfile ||
                  loadingDeleteMobileProfile
                }
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === ModalType.UPDATE
                  ? "Update mobile profile"
                  : "Add mobile profile"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
