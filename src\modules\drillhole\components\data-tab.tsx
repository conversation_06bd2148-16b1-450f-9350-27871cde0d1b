"use client";

import { RequestState } from "@/common/configs/app.contants";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";
import { useGetAssayData } from "@/modules/downhole-point/hooks/useGetAssayData";
import { useGetDownholeByProject } from "@/modules/downhole-point/hooks/useGetDownholeByProject";
import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { getDetailGeologySuite } from "@/modules/geology-suite/redux/thunks";

import ModalEntryData from "@/modules/logging/components/modal-entry-data";
import { useGetListGeotechData } from "@/modules/logging/hooks/useGetListGeotechData";
import { getLoggingInfo } from "@/modules/logging/redux/loggingSlice/thunks";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import { TableColumnsType, Tooltip } from "antd";
import { useParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { ModalGeotechDataDrillhole } from "./modal-geotech-data-drillhole";
import { GiStonePile } from "react-icons/gi";
import {
  convertColumnNameToDisplay,
  ILoggingEntry,
} from "@/modules/logging/components/logging-entries-grid";

export interface IDrillholeDetailProps {
  id: string;
  type: "geology" | "assay" | "geophysics" | "geotech";
  drillHoleName: string;
  projectId: string;
}

export function DataTab({
  id,
  type,
  drillHoleName,
  projectId,
}: IDrillholeDetailProps) {
  const [modalGeotechData, setModalGeotechData] = useState<{
    detail?: any;
    type: "edit" | "delete";
    isOpen: boolean;
  }>({
    detail: undefined,
    type: "edit",
    isOpen: false,
  });
  const [modalState, setModalState] = useState<{
    detail?: ILoggingEntry;
    type: "create" | "delete";
    isOpen: boolean;
  }>({
    detail: undefined,
    type: "create",
    isOpen: false,
  });

  const {
    data: downholeData,
    request: requestDownholeData,
    loading: loadingDownholeData,
  } = useGetDownholeByProject();
  const {
    data: assayData,
    request: requestAssayData,
    loading: loadingAssayData,
  } = useGetAssayData();
  const { id: drillholeId } = useParams();

  const [columns, setColumns] = useState<TableColumnsType<any>>([]);
  const dispatch = useAppDispatch();
  const { allLoggings, getLoggingInfoStatus } = useAppSelector(
    (state) => state.logging
  );
  const loadingGeologyData = getLoggingInfoStatus === RequestState.pending;
  const refetchDataEntry = () => {
    dispatch(
      getLoggingInfo({
        DrillholeId: Number(drillholeId),
        GeologySuiteId: Number(id),
      })
    );
    dispatch(getDetailGeologySuite(id));
  };
  const {
    data: geotechData,
    request: requestGeotechData,
    loading: loadingGeotechData,
  } = useGetListGeotechData();
  useEffect(() => {
    if (type === "geology") {
      refetchDataEntry();
    }
    if (type === "assay") {
      requestAssayData(
        {
          AssaySuitId: id,
          DrillHoleName: [drillHoleName],
          projectId: [projectId],
        },
        (data) => {
          let columns: TableColumnsType<any> = [];
          if (data.length === 0) return;
          const fields = Object.keys(data[0]);

          fields.forEach((item: any) => {
            if (item === "DrillHole") {
              columns.push({
                title: item,
                dataIndex: item,
                key: item,
              });
            }
          });

          // Thêm các cột còn lại
          fields.forEach((item: any) => {
            if (item !== "DrillHole" && item !== "groupId") {
              columns.push({
                title: item,
                dataIndex: item,
                key: item,
                width: 150,
              });
            }
          });
          setColumns(columns);
        }
      );
    }

    if (type === "geophysics") {
      requestDownholeData(
        {
          GeophysicsSuitId: id,
          DrillHoleName: drillHoleName,
          // projectId: [projectId],
        },
        (data) => {
          let columns: TableColumnsType<any> = [];
          if (data.length === 0) return;
          const fields = Object.keys(data[0]);

          fields.forEach((item: any) => {
            if (item === "DrillHole") {
              columns.push({
                title: item,
                dataIndex: item,
                key: item,
              });
            }
          });

          // Thêm các cột còn lại
          fields.forEach((item: any) => {
            if (item !== "DrillHole" && item !== "groupId") {
              columns.push({
                title: item,
                dataIndex: item,
                key: item,
                width: 150,
              });
            }
          });
          setColumns(columns);
        }
      );
    }
    if (type === "geotech") {
      requestGeotechData(
        {
          DrillHoleId: Number(drillholeId),
          GeotechSuiteId: Number(id),
        },
        (data) => {
          let columns: TableColumnsType<any> = [
            {
              title: "Width",
              dataIndex: "width",
              key: "width",
              sorter: (a, b) => a.width - b.width,
            },
            {
              title: "Alpha Angle",
              dataIndex: "alphaAngle",
              key: "alphaAngle",
              width: 150,

              sorter: (a, b) => a.alphaAngle - b.alphaAngle,
            },
            {
              title: "Beta Angle",
              dataIndex: "betaAngle",
              key: "betaAngle",
              width: 150,

              sorter: (a, b) => a.betaAngle - b.betaAngle,
            },
            {
              title: "Depth",
              dataIndex: "depth",
              key: "depth",
              sorter: (a, b) => a.depth - b.depth,
              render(value, record, index) {
                return (
                  <div>
                    {record?.depth} {record?.depthTo && `- ${record?.depthTo}`}
                  </div>
                );
              },
            },
            // {
            //   title: "Actions",
            //   dataIndex: "actions",
            //   key: "actions",
            //   render: (value, record, index) => {
            //     return (
            //       <div className="flex gap-2">
            //         <div className="flex justify-end gap-2">
            //           <Button
            //             type="text"
            //             icon={<EditOutlined />}
            //             onClick={() => {
            //               setModalGeotechData({
            //                 detail: record,
            //                 type: "edit",
            //                 isOpen: true,
            //               });
            //             }}
            //           />
            //         </div>
            //       </div>
            //     );
            //   },
            // },
            {
              title: "Structure",
              dataIndex: "structure",
              key: "structure",
              render: (value, record, index) => {
                return <div>{record?.structure?.name}</div>;
              },
            },
            {
              title: "Rock Group",
              dataIndex: "rockGroup",
              key: "rockGroup",
              render: (value, record, index) => {
                return <div>{record?.rockGroup?.name}</div>;
              },
            },
          ];
          if (data?.items?.length === 0) return;

          setColumns(columns);
        }
      );
    }
  }, [id, type]);

  const processLoggingEntry = (item: ILoggingEntry) => {
    const newItem = { ...item };

    item.dataEntryValues.forEach((value, index) => {
      switch (value.fieldType) {
        case FieldType.Colour:
          newItem[`color#${value?.fieldName}#${index}`] =
            value?.colour?.hexCode ?? "N/A";
          break;
        case FieldType.Description:
          newItem[`description#${value?.fieldName}#${index}`] =
            value?.description ?? "N/A";
          break;
        case FieldType.RockGroup:
          newItem[`rockType#${value?.fieldName}#${index}`] =
            value?.rockType?.name ?? "N/A";
          break;
        case FieldType.RockType:
          newItem[`rockTypeNumber#${value?.fieldName}#${index}`] =
            value?.numberValue
              ? `${value?.numberValue} ${value?.number?.unit?.code}-${value?.rockType?.code}`
              : "N/A";
          break;
        case FieldType.DateField:
          newItem[`date#${value?.fieldName}#${index}`] =
            new Date(value?.dateValue).toLocaleDateString() ?? "N/A";
          break;
        case FieldType.PickList:
          newItem[`pickList#${value?.fieldName}#${index}`] =
            value?.pickListItem?.name ?? "N/A";
          break;
        case FieldType.RockSelect:
          newItem[
            `rockTypeSelect#${value?.fieldName}#${index}`
          ] = `${value?.rockType?.name}-${value?.rockType?.code}`;
          break;
        case FieldType.NumberField:
          newItem[`number#${value?.fieldName}#${index}`] = `${
            value?.numberValue ?? "N/A"
          }`;
          break;
        case FieldType.RockTree:
          newItem[`rockTree#${value?.fieldName}#${index}`] = `${
            value?.rockNode?.name ?? "N/A"
          }`;
          break;
      }
    });
    return newItem;
  };

  const createColumns = (items: any[], allLoggings: any[]) => {
    const excludedKeys = [
      "id",
      "geologySuiteId",
      "drillholeId",
      "dataEntryValues",
      "imageCropId",
      "x",
    ];
    const depthColumns = ["depthFrom", "depthTo"];
    const columns: any[] = [];
    // Add actions column
    columns.push({
      title: "Actions",
      key: "actions",
      width: 70,
      render: (_, record) => (
        <div className="flex gap-3">
          <Tooltip title="Edit entry">
            <EditOutlined
              onClick={() => {
                const logging = allLoggings.find(
                  (item) => item.id === record.id
                );
                setModalState({
                  detail: logging,
                  type: "create",
                  isOpen: true,
                });
              }}
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
          </Tooltip>
          <Tooltip title="Delete entry">
            <DeleteOutlined
              onClick={() =>
                setModalState({
                  detail: record,
                  type: "delete",
                  isOpen: true,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
          </Tooltip>
        </div>
      ),
    });

    // Add depth columns first
    depthColumns.forEach((key) => {
      columns.push({
        title: convertColumnNameToDisplay(key),
        dataIndex: key,
        key: key,
        width: 100,
        sorter: (a, b) => a[key] - b[key],
        render: (value) => value?.toFixed(2),
      });
    });

    // Add dynamic columns
    const uniqueKeys = new Set<string>();
    console.log("item: ", items);

    items.forEach((item) => {
      Object.keys(item).forEach((key) => {
        if (
          !excludedKeys.includes(key) &&
          !depthColumns.includes(key) &&
          !uniqueKeys.has(key)
        ) {
          uniqueKeys.add(key);
          const title = key.split("#")[1];

          let renderFunction;
          if (key.includes("color")) {
            renderFunction = (value) => (
              <div
                style={{
                  backgroundColor: value,
                  width: 60,
                  height: 20,
                  borderRadius: 10,
                }}
              />
            );
          } else if (key.includes("description")) {
            renderFunction = (value) => (
              <Tooltip title={value}>
                <div className="line-clamp-1">{value}</div>
              </Tooltip>
            );
          } else if (key.includes("rockTree")) {
            renderFunction = (value) => (
              <div className="flex items-center">
                <GiStonePile className="mr-1 text-lg" />
                {value}
              </div>
            );
          }

          columns.push({
            title,
            dataIndex: key,
            key: key,
            width: 140,
            render: renderFunction,
          });
        }
      });
    });

    return columns;
  };
  // Process logging data
  const newLoggings = useMemo(
    () => allLoggings.map(processLoggingEntry),
    [allLoggings]
  );
  // Create columns
  const columnGeologySuite = useMemo(
    () => createColumns(newLoggings, allLoggings),
    [newLoggings, allLoggings]
  );

  return (
    <>
      <ModalGeotechDataDrillhole
        modalState={modalGeotechData}
        setModalState={setModalGeotechData}
      />
      <ModalEntryData
        modalState={modalState}
        setModalState={setModalState}
        refetchDataEntry={refetchDataEntry}
      />
      <TableCommon
        className="font-visby"
        pagination={{
          pageSize: 50,
        }}
        scroll={{ x: "max-content", y: 6 * 110 }}
        columns={type === "geology" ? columnGeologySuite : columns}
        dataSource={
          type === "geology"
            ? newLoggings
            : type === "assay"
            ? assayData
            : type === "geotech"
            ? geotechData
            : downholeData
        }
        loading={
          type === "geology"
            ? loadingGeologyData
            : type === "assay"
            ? loadingAssayData
            : type === "geotech"
            ? loadingGeotechData
            : loadingDownholeData
        }
      />
    </>
  );
}
