import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import UploadImages from "@/modules/image/components/upload-images";

export default function Page() {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <div className="max-h-[calc(100vh-48px)] overflow-auto">
        <UploadImages />
      </div>
    </PermissionProvider>
  );
}
