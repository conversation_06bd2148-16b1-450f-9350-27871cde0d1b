import { debounce } from "lodash";
import { useRouter, useSearchParams } from "next/navigation";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";

import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { useGetListGeologySuite } from "@/modules/geology-suite/hooks/useGetListGeologySuite";
import { useGetListGeotechSuite } from "@/modules/geotech-suite/hooks/useGetListGeotechSuite";
import {
  updateLoggingSuiteSelected,
  updateLoggingSuiteMode,
  updateLoggingSuiteId,
  updateGeotechData,
} from "../../redux/loggingSlice";
import { updateFontSize } from "@/modules/auth/redux/userSlice";
import { adjustFontSize } from "../../utils/logging.utils";
import { LoggingGridProps } from "./table-geology-types";
import { LoggingGridEmpty } from "./logging-grid-empty";
import { TableGeotechDisplay } from "./table-geotech-display";
import { TableGeology } from "./table-geology";

import "./logging-grid.css";

export const LoggingGrid = memo<LoggingGridProps>(({ refetchDataEntry }) => {
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();

  // Get query parameters
  const queries: any = {};
  for (const [key, value] of searchParams.entries()) {
    const arrayValues = searchParams.getAll(key);
    queries[key] = arrayValues.length > 1 ? arrayValues : value;
  }

  // Redux state
  const { loggingSuiteMode } = useAppSelector((state) => state.logging);
  const reduxFontSize = useAppSelector((state) => state.user.fontSize);
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );

  // Get collapseLogging state for responsive width calculation
  const collapseLogging = useAppSelector((state) => state.user.collapseLogging);

  // Local state
  const [searchText, setSearchText] = useState("");

  // Dynamic width state and container reference
  const [dynamicWidth, setDynamicWidth] = useState<number>(1000);
  const containerRef = React.useRef<HTMLDivElement>(null);

  // Additional data loading hooks for header functionality
  const { request: getGeologySuites, data: geologySuites } =
    useGetListGeologySuite();
  const { request: getGeotechSuites, data: geotechSuites } =
    useGetListGeotechSuite();
  const router = useRouter();

  // Dynamic width calculation based on parent container and collapseLogging state
  const calculateDynamicWidth = useCallback(() => {
    if (containerRef.current) {
      const parentElement = containerRef.current.parentElement;
      if (parentElement) {
        const parentWidth = parentElement.clientWidth;

        // Reserve some padding/margin space (e.g., 32px total)
        const availableWidth = Math.max(parentWidth, 800); // Minimum 800px
        setDynamicWidth(availableWidth);
      }
    }
  }, []);
  const loggingSuiteIdParams = queries?.loggingSuiteId;

  // Effect to handle width recalculation when collapseLogging changes
  useEffect(() => {
    // Small delay to allow layout changes to complete
    const timeoutId = setTimeout(() => {
      calculateDynamicWidth();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [collapseLogging, calculateDynamicWidth, loggingSuiteIdParams]);

  // Effect to handle window resize
  useEffect(() => {
    const handleResize = () => {
      calculateDynamicWidth();
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [calculateDynamicWidth]);

  // Load options data - only once on mount
  useEffect(() => {
    getGeologySuites({
      isActive: true,
      maxResultCount: 1000,
      projectId: globalProjectId,
    });
    getGeotechSuites({
      isActive: true,
      maxResultCount: 1000,
      projectId: globalProjectId,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [globalProjectId]); // Empty dependency array - only run once on mount

  // Get URL parameters

  // Synchronize suite selection when URL parameters change
  useEffect(() => {
    if (loggingSuiteIdParams) {
      // This ensures the Redux state is updated when URL changes from main component
      dispatch(updateLoggingSuiteSelected(loggingSuiteIdParams));

      const suiteId = loggingSuiteIdParams.split("-")[1];
      if (loggingSuiteIdParams.startsWith("geologySuites")) {
        dispatch(updateLoggingSuiteMode("Geology"));
      } else if (loggingSuiteIdParams.startsWith("geotechSuites")) {
        dispatch(updateLoggingSuiteMode("Geotech"));
        dispatch(updateLoggingSuiteId(Number(suiteId)));
      }
    }
  }, [loggingSuiteIdParams, dispatch]);

  // Header functionality
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchText(value);
      }, 300),
    []
  );

  const handleSearch = useCallback(
    (value: string) => {
      debouncedSearch(value);
    },
    [debouncedSearch]
  );

  const handleFontSizeChange = useCallback(
    (increment: boolean) => {
      dispatch(updateFontSize(adjustFontSize(reduxFontSize, increment)));
    },
    [dispatch, reduxFontSize]
  );

  const handleLoggingSuiteChange = useCallback(
    (value: string) => {
      const newParams = new URLSearchParams(window.location.search);
      if (!value) {
        newParams.delete("loggingSuiteId");
        newParams.delete("geotechSuiteId");
      } else {
        newParams.set("loggingSuiteId", value);
        newParams.delete("geotechSuiteId");
      }

      // Update Redux state to synchronize with main logging component
      dispatch(updateGeotechData([]));
      dispatch(updateLoggingSuiteSelected(value));

      // Parse suite type and ID
      if (value) {
        const suiteId = value.split("-")[1];
        if (value.startsWith("geologySuites")) {
          dispatch(updateLoggingSuiteMode("Geology"));
          // Note: We don't call getDetailGeologySuite here as it's handled by the main component
        } else if (value.startsWith("geotechSuites")) {
          dispatch(updateLoggingSuiteMode("Geotech"));
          dispatch(updateLoggingSuiteId(Number(suiteId)));
        }
      }

      router.push(`${window.location.pathname}?${newParams.toString()}`);
    },
    [router, dispatch]
  );

  // Render empty state if no logging suite is selected
  if (!loggingSuiteIdParams) {
    return <LoggingGridEmpty />;
  }

  return (
    <div
      ref={containerRef}
      className="flex flex-col h-full max-h-full overflow-hidden relative logging-grid"
      style={{
        fontSize: `${reduxFontSize}px`,
        width: `${dynamicWidth}px`,
        // height: `${dynamicHeight}px`,
        transition: "width 0.3s ease-in-out", // Smooth transition during width changes
      }}
    >
      {/* Conditional Table Rendering */}
      {loggingSuiteMode === "Geotech" ? (
        /* Geotech Read-only Table */
        <TableGeotechDisplay
          searchText={searchText}
          fontSize={reduxFontSize}
          dynamicWidth={dynamicWidth}
          handleFontSizeChange={handleFontSizeChange}
          loggingSuiteIdParams={loggingSuiteIdParams}
          geologySuites={geologySuites}
          geotechSuites={geotechSuites}
          loading={false}
          handleLoggingSuiteChange={handleLoggingSuiteChange}
          handleSearch={handleSearch}
        />
      ) : (
        /* Geology Editable Table */
        <TableGeology
          searchText={searchText}
          fontSize={reduxFontSize}
          dynamicWidth={dynamicWidth}
          handleFontSizeChange={handleFontSizeChange}
          refetchDataEntry={refetchDataEntry}
          loggingSuiteIdParams={loggingSuiteIdParams}
          geologySuites={geologySuites}
          geotechSuites={geotechSuites}
          loading={false}
          handleLoggingSuiteChange={handleLoggingSuiteChange}
          handleSearch={handleSearch}
        />
      )}
    </div>
  );
});

LoggingGrid.displayName = "LoggingGrid";
