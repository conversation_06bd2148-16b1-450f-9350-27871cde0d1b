"use client";
import React, { useState, useEffect } from "react";
import { Input, Checkbox, Empty } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { SearchOutlined } from "@ant-design/icons";
import { toggleDrillholeVisibility } from "../redux/threedSlice/threed.slice";
import {
  selectDrillholes,
  selectVisibilityMap,
  selectCoordinateBase,
} from "../redux/threedSlice/selectors";
import { DrillholeData } from "../interface/threed.interface";
import { normalizeCoordinatesWithBase } from "../helpers/drillhole.helper";

interface DrillholeListPanelProps {}

const DrillholeListPanel: React.FC<DrillholeListPanelProps> = ({}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const dispatch = useDispatch();
  const [filteredDrillholes, setFilteredDrillholes] = useState<DrillholeData[]>(
    []
  );

  const drillholesState = useSelector(selectDrillholes);
  const drillholesData = drillholesState.data || [];

  // Get visibility map from Redux store
  let visibilityMap: Record<string, boolean> = {};
  let coordinateBase: { east: number; north: number; rl: number } | null = null;
  try {
    visibilityMap = useSelector(selectVisibilityMap);
    coordinateBase = useSelector(selectCoordinateBase);
  } catch (error) {
    console.log(
      "Error selecting from threed slice - might not be registered yet"
    );
  }

  // Filter drillholes based on search term
  useEffect(() => {
    if (drillholesData.length > 0) {
      const lowercaseSearchTerm = searchTerm.toLowerCase();
      const filtered = drillholesData.filter((drillhole) =>
        drillhole.name.toLowerCase().includes(lowercaseSearchTerm)
      );
      setFilteredDrillholes(filtered);
    } else {
      setFilteredDrillholes([]);
    }
  }, [searchTerm, drillholesData]);

  // Handle visibility toggle for a drillhole
  const handleToggleVisibility = (id: string, checked: boolean) => {
    // @ts-ignore - Bypassing type check temporarily until we resolve the Redux store typing
    dispatch(toggleDrillholeVisibility({ id, visible: checked }));
  };

  // Handle drillhole selection and center camera on it
  const handleDrillholeClick = (drillhole: DrillholeData) => {
    // Toggle drillhole visibility first
    const currentVisibility = visibilityMap[drillhole.id] !== false;
    handleToggleVisibility(drillhole.id, !currentVisibility);

    // Validate drillhole coordinates
    if (
      typeof drillhole.east !== "number" ||
      typeof drillhole.north !== "number" ||
      typeof drillhole.rl !== "number"
    ) {
      console.error("Invalid coordinates for drillhole:", drillhole.id);
      return;
    }

    // Normalize coordinates using the coordinate base
    const normalizedPosition = normalizeCoordinatesWithBase(
      drillhole.east,
      drillhole.north,
      drillhole.rl,
      coordinateBase
    );

    // Create camera control event to center on the drillhole
    const cameraEvent = new CustomEvent("camera-control", {
      detail: {
        target: [
          normalizedPosition.x,
          normalizedPosition.y,
          normalizedPosition.z,
        ],
      },
    });
    window.dispatchEvent(cameraEvent);
  };

  return (
    <>
      <div className="mb-2">
        <Input
          placeholder="Search drillholes..."
          prefix={<SearchOutlined />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          allowClear
          className="w-full"
        />
      </div>
      {/* Select All / Deselect All buttons */}
      <div className="flex gap-2 mb-2">
        <button
          className="px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
          onClick={() => {
            filteredDrillholes.forEach((drillhole) => {
              if (visibilityMap[drillhole.id] !== true) {
                handleToggleVisibility(drillhole.id, true);
              }
            });
          }}
        >
          Select All
        </button>
        <button
          className="px-2 py-1 bg-gray-400 text-white rounded text-xs hover:bg-gray-500"
          onClick={() => {
            filteredDrillholes.forEach((drillhole) => {
              if (visibilityMap[drillhole.id] !== false) {
                handleToggleVisibility(drillhole.id, false);
              }
            });
          }}
        >
          Deselect All
        </button>
      </div>
      <div className="overflow-auto">
        {filteredDrillholes.length === 0 ? (
          <Empty
            description="No drillholes found"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className="my-4"
          />
        ) : (
          <div className="flex flex-wrap gap-1 max-h-[300px] h-full overflow-y-auto">
            {filteredDrillholes.map((drillhole) => (
              <div
                key={drillhole.id}
                className="bg-gray-50 rounded px-1.5 py-0.5 mb-1 w-full cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => handleDrillholeClick(drillhole)}
              >
                <Checkbox
                  checked={visibilityMap[drillhole.id] !== false} // Show if not explicitly hidden
                  onChange={(e) => {
                    e.stopPropagation(); // Prevent triggering the click event when clicking checkbox
                    // No need to toggle here since handleDrillholeClick handles it
                  }}
                >
                  <span className="text-sm truncate">{drillhole.name}</span>
                </Checkbox>
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default DrillholeListPanel;
